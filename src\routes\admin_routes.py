"""
Administrative routes for debugging and configuration.
"""

from flask import request, jsonify

from ..core.database import collection
from ..core.config import GPU_REST_DELAY, N8N_ONFAIL_WEBHOOK_URL
from ..core.utils import create_error_response, create_success_response, log_and_return_error


def print_all(app):
    """Print all documents to console for debugging."""
    try:
        results = collection.get(include=["documents", "metadatas", "embeddings"])
        app.logger.info("Printing all documents in ChromaDB")

        for i in range(len(results["ids"])):
            print(f"ID: {results['ids'][i]}")
            print(f"Document: {results['documents'][i]}")
            print(f"Metadata: {results['metadatas'][i]}")
            print("-" * 40)

        return jsonify({"status": "ok", "total": len(results["ids"])})

    except Exception as e:
        app.logger.error(f"Print operation failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def delete_all(app):
    """Delete all documents from the collection."""
    try:
        all_data = collection.get(include=[])
        ids = all_data.get("ids", [])

        if not ids:
            return jsonify({"status": "ok", "message": "No records to delete"})

        collection.delete(ids=ids)
        app.logger.info(f"Deleted {len(ids)} records from collection")
        return jsonify({"status": "ok", "message": f"Deleted {len(ids)} items"})

    except Exception as e:
        app.logger.error(f"Delete operation failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def web_interface():
    """Serve the web management interface."""
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return """
        <h1>Error: index.html not found</h1>
        <p>Please make sure index.html exists in the project directory.</p>
        """


def export_data(app):
    """Export all data from ChromaDB collection."""
    try:
        results = collection.get(include=["documents", "metadatas", "embeddings"])
        
        export_data = {
            "collection_name": collection.name,
            "total_documents": len(results.get("ids", [])),
            "data": {
                "ids": results.get("ids", []),
                "documents": results.get("documents", []),
                "metadatas": results.get("metadatas", []),
                "embeddings": results.get("embeddings", [])
            }
        }
        
        return jsonify({
            "status": "ok",
            "export": export_data
        })
        
    except Exception as e:
        app.logger.error(f"Export failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500


def get_gpu_delay():
    """Get current GPU delay setting."""
    return jsonify({
        "status": "ok",
        "gpu_delay_seconds": GPU_REST_DELAY
    })


def set_gpu_delay(app):
    """Set GPU delay between chunk processing."""
    try:
        data = request.get_json()
        new_delay = data.get("delay_seconds")
        
        if new_delay is None:
            return create_error_response("delay_seconds parameter required", 400, "validation_error")
        
        if not isinstance(new_delay, (int, float)) or new_delay < 0:
            return create_error_response("delay_seconds must be a non-negative number", 400, "validation_error")
        
        # Update the global variable (note: this won't persist across restarts)
        global GPU_REST_DELAY
        old_delay = GPU_REST_DELAY
        GPU_REST_DELAY = new_delay
        
        return create_success_response({
            "old_delay_seconds": old_delay,
            "new_delay_seconds": new_delay
        }, f"GPU delay updated to {new_delay} seconds")
        
    except Exception as e:
        return log_and_return_error(
            app.logger,
            f"Failed to update GPU delay: {e}",
            "Failed to update GPU delay",
            500
        )


def get_onfail_webhook():
    """Get current OnFail webhook URL."""
    return jsonify({
        "status": "ok",
        "onfail_webhook_url": N8N_ONFAIL_WEBHOOK_URL
    })


def set_onfail_webhook(app):
    """Set OnFail webhook URL."""
    try:
        data = request.get_json()
        new_url = data.get("webhook_url")
        
        if not new_url:
            return create_error_response("webhook_url parameter required", 400, "validation_error")
        
        if not isinstance(new_url, str):
            return create_error_response("webhook_url must be a string", 400, "validation_error")
        
        # Update the global variable (note: this won't persist across restarts)
        global N8N_ONFAIL_WEBHOOK_URL
        old_url = N8N_ONFAIL_WEBHOOK_URL
        N8N_ONFAIL_WEBHOOK_URL = new_url
        
        return create_success_response({
            "old_webhook_url": old_url,
            "new_webhook_url": new_url
        }, "OnFail webhook URL updated successfully")
        
    except Exception as e:
        return log_and_return_error(
            app.logger,
            f"Failed to update OnFail webhook URL: {e}",
            "Failed to update OnFail webhook URL",
            500
        )
