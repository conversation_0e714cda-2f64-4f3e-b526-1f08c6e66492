/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

/* Container and layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* Navigation tabs */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.nav-tab {
    flex: 1;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
}

.nav-tab.active {
    background: white;
    color: #007bff;
    border-bottom: 3px solid #007bff;
}

.nav-tab:hover {
    background: #e9ecef;
    color: #495057;
}

/* Tab content */
.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* Form elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220,53,69,0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40,167,69,0.3);
}

/* Alerts */
.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Document grid and cards */
.documents-grid {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.document-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.document-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.document-id {
    font-weight: bold;
    color: #007bff;
    margin-bottom: 10px;
    font-size: 14px;
}

.document-content {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    max-height: 200px;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.5;
}

.document-metadata {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 15px;
}

.metadata-preview-search {
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.document-actions {
    display: flex;
    gap: 10px;
}

/* Stats grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 10px;
    text-align: center;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
}

/* Loading and spinner */
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search box */
.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box input {
    padding-left: 40px;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 5px;
    cursor: pointer;
}

.pagination button:hover {
    background: #f8f9fa;
}

.pagination button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* File upload area */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.file-upload-area.dragover {
    border-color: #007bff;
    background: #f8f9ff;
}

/* Database Management Styles */
.database-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.database-info h3 {
    margin: 0;
    color: #333;
}

.database-info p {
    margin: 5px 0 0 0;
    color: #6c757d;
    font-size: 14px;
}

.database-actions {
    display: flex;
    gap: 10px;
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-pagination-info {
    color: #6c757d;
    font-size: 14px;
}

/* Data Table Styles */
.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.data-table th.sortable:hover {
    background: #e9ecef;
}

.sort-indicator {
    margin-left: 5px;
    opacity: 0.5;
}

.sort-indicator.asc::after {
    content: "↑";
    opacity: 1;
}

.sort-indicator.desc::after {
    content: "↓";
    opacity: 1;
}

.data-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

.data-table tbody tr:nth-child(even) {
    background: #fdfdfd;
}

.data-table tbody tr:nth-child(even):hover {
    background: #f8f9fa;
}

.loading-cell {
    text-align: center;
    padding: 40px;
}

/* Table cell content styles */
.cell-id {
    font-family: monospace;
    font-weight: bold;
    color: #007bff;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cell-document {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
}

.cell-metadata {
    font-family: monospace;
    font-size: 12px;
    color: #6c757d;
    max-width: 300px;
    padding: 8px;
}

.metadata-preview {
    margin-bottom: 8px;
    line-height: 1.4;
}

.metadata-field {
    display: block;
    margin-bottom: 2px;
}

.metadata-empty {
    color: #999;
    font-style: italic;
}

.metadata-more {
    color: #007bff;
    font-size: 11px;
    font-weight: bold;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.cell-actions {
    white-space: nowrap;
}

.cell-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    margin-right: 5px;
}

/* Table Pagination */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-info {
    color: #6c757d;
    font-size: 14px;
}

.page-size-control {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.page-size-control select {
    padding: 5px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease;
}

.modal-large {
    max-width: 800px;
    width: 95%;
}

/* Metadata Modal Styles */
.metadata-info {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.metadata-content {
    margin-bottom: 25px;
}

.metadata-content h4 {
    margin-bottom: 15px;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.metadata-field-detail {
    display: flex;
    margin-bottom: 12px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.metadata-key {
    font-weight: bold;
    color: #495057;
    min-width: 120px;
    margin-right: 15px;
    font-family: monospace;
}

.metadata-value {
    flex: 1;
    font-family: monospace;
    word-break: break-word;
}

.metadata-string {
    color: #28a745;
}

.metadata-number {
    color: #007bff;
}

.metadata-boolean {
    color: #6f42c1;
}

.metadata-null {
    color: #6c757d;
    font-style: italic;
}

.metadata-object {
    color: #fd7e14;
}

.metadata-raw {
    margin-top: 25px;
}

.metadata-raw h4 {
    margin-bottom: 10px;
    color: #495057;
}

.metadata-raw pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    max-height: 300px;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover,
.close:focus {
    color: #000;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 10px 10px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tab {
        flex: 1 1 50%;
        min-width: 120px;
    }

    .database-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .database-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .table-controls {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 600px;
    }

    .table-pagination {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .modal-footer {
        flex-direction: column;
    }

    .cell-document {
        max-width: 200px;
    }

    .cell-metadata {
        max-width: 200px;
    }

    .metadata-field-detail {
        flex-direction: column;
    }

    .metadata-key {
        min-width: auto;
        margin-right: 0;
        margin-bottom: 5px;
    }
}