"""
Administrative and management functionality.
"""

import threading
import time
from datetime import datetime
from ..core.state import (
    queue_lock, processing_status, chunk_queues, plan_b_sessions
)
from ..core.config import GPU_REST_DELAY
from .n8n_integration import _send_chunk_to_n8n, _send_chunk_to_onfail_webhook


def _send_next_chunk_with_delay(session_id, chunk_info, delay_seconds):
    """Send the next chunk after a delay (used for GPU rest)."""
    def delayed_send():
        time.sleep(delay_seconds)
        
        chunk_text, chunk_filename = chunk_info
        chunk_number = int(chunk_filename.split('_chunk_')[1]) if '_chunk_' in chunk_filename else 1
        
        # Extract original filename for consistent source naming
        original_filename = chunk_filename.split("_chunk_")[0] if "_chunk_" in chunk_filename else chunk_filename
        success = _send_chunk_to_n8n(chunk_text, chunk_filename, session_id, chunk_number, original_filename=original_filename)
        
        if not success:
            with queue_lock:
                if session_id in processing_status:
                    processing_status[session_id]['error'] = f"Failed to send chunk {chunk_filename}"
                    processing_status[session_id]['status'] = 'error'
    
    # Start the delayed send in a separate thread
    thread = threading.Thread(target=delayed_send)
    thread.daemon = True
    thread.start()


def get_chunk_status(session_id):
    """Get the current status of chunk processing for a session."""
    with queue_lock:
        if session_id in processing_status:
            status = processing_status[session_id].copy()
            # Convert datetime to string for JSON serialization
            if status.get('last_update'):
                status['last_update'] = status['last_update'].isoformat()
            return status
        return None


def get_plan_b_status(session_id):
    """Get the current status of Plan B processing for a session."""
    with queue_lock:
        if session_id in plan_b_sessions:
            status = plan_b_sessions[session_id].copy()
            # Convert datetime to string for JSON serialization
            if status.get('created_at'):
                status['created_at'] = status['created_at'].isoformat()
            return status
        return None
