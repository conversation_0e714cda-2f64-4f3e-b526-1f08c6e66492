#!/usr/bin/env python3
"""
Test script for the Process Queue Management System.

This script tests the queue system to ensure it properly limits concurrent processes
and handles queue progression correctly.
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5555"
TEST_TIMEOUT = 60  # seconds

def test_queue_status_endpoint():
    """Test the queue status endpoint."""
    print("🔍 Testing queue status endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/queue-status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Queue status endpoint working")
            print(f"   Active processes: {data['queue']['active_count']}/{data['queue']['max_concurrent']}")
            print(f"   Pending queue: {data['queue']['pending_count']}")
            return True
        else:
            print(f"❌ Queue status endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing queue status endpoint: {e}")
        return False

def upload_test_youtube_transcript(title, content):
    """Upload a test YouTube transcript."""
    data = {
        "transcript_text": content,
        "video_title": title,
        "video_url": f"https://youtube.com/watch?v=test_{title.replace(' ', '_')}",
        "source": "test_transcripts"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/upload-youtube-transcript", 
                               json=data, 
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            return result.get('data', {}).get('session_id'), result.get('data', {}).get('queue_status')
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return None, None
    except Exception as e:
        print(f"❌ Error uploading transcript: {e}")
        return None, None

def check_session_status(session_id):
    """Check the status of a specific session."""
    try:
        response = requests.get(f"{BASE_URL}/api/session-status/{session_id}")
        if response.status_code == 200:
            return response.json().get('session', {})
        return None
    except Exception as e:
        print(f"❌ Error checking session status: {e}")
        return None

def test_concurrent_limit():
    """Test that the system properly limits concurrent processes to 3."""
    print("\n🧪 Testing concurrent process limit (max 3)...")
    
    # Create test content
    test_transcripts = [
        ("Test Video 1", "This is test content for video 1. " * 100),
        ("Test Video 2", "This is test content for video 2. " * 100),
        ("Test Video 3", "This is test content for video 3. " * 100),
        ("Test Video 4", "This is test content for video 4. " * 100),
        ("Test Video 5", "This is test content for video 5. " * 100),
    ]
    
    session_ids = []
    queue_statuses = []
    
    # Upload all test transcripts quickly
    print("📤 Uploading 5 test transcripts...")
    for title, content in test_transcripts:
        session_id, queue_status = upload_test_youtube_transcript(title, content)
        if session_id:
            session_ids.append(session_id)
            queue_statuses.append(queue_status)
            print(f"   {title}: {queue_status} (session: {session_id[:8]}...)")
        else:
            print(f"   {title}: FAILED")
    
    # Check queue status
    print("\n📊 Checking queue status...")
    queue_status = requests.get(f"{BASE_URL}/api/queue-status").json()
    
    active_count = queue_status['queue']['active_count']
    pending_count = queue_status['queue']['pending_count']
    
    print(f"   Active processes: {active_count}")
    print(f"   Pending in queue: {pending_count}")
    
    # Verify limits
    if active_count <= 3:
        print("✅ Concurrent limit respected (≤ 3 active processes)")
    else:
        print(f"❌ Concurrent limit violated ({active_count} > 3 active processes)")
        return False
    
    if len(session_ids) > 3 and pending_count > 0:
        print("✅ Queue system working (excess uploads queued)")
    elif len(session_ids) <= 3:
        print("✅ All uploads fit within limit (no queuing needed)")
    else:
        print("❌ Queue system not working properly")
        return False
    
    return True, session_ids

def test_queue_progression():
    """Test that queued processes start when active ones complete."""
    print("\n⏳ Testing queue progression...")
    print("   (This test requires manual observation or longer wait times)")
    
    # Check initial queue status
    initial_status = requests.get(f"{BASE_URL}/api/queue-status").json()
    initial_active = initial_status['queue']['active_count']
    initial_pending = initial_status['queue']['pending_count']
    
    print(f"   Initial state: {initial_active} active, {initial_pending} pending")
    
    if initial_pending == 0:
        print("   No pending processes to test progression")
        return True
    
    # Monitor for changes over time
    print("   Monitoring queue for 30 seconds...")
    start_time = time.time()
    
    while time.time() - start_time < 30:
        current_status = requests.get(f"{BASE_URL}/api/queue-status").json()
        current_active = current_status['queue']['active_count']
        current_pending = current_status['queue']['pending_count']
        
        if current_pending < initial_pending:
            print(f"✅ Queue progression detected: {initial_pending} -> {current_pending} pending")
            return True
        
        time.sleep(2)
    
    print("   No queue progression observed in 30 seconds")
    print("   (This may be normal if processes are still running)")
    return True

def test_api_endpoints():
    """Test all queue-related API endpoints."""
    print("\n🔌 Testing API endpoints...")
    
    endpoints = [
        ("/api/queue-status", "GET"),
        ("/api/queue-notifications", "GET"),
    ]
    
    all_passed = True
    
    for endpoint, method in endpoints:
        try:
            response = requests.request(method, f"{BASE_URL}{endpoint}")
            if response.status_code == 200:
                print(f"✅ {method} {endpoint}")
            else:
                print(f"❌ {method} {endpoint} - Status: {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")
            all_passed = False
    
    return all_passed

def main():
    """Run all queue system tests."""
    print("🚀 Process Queue Management System Test")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Server is not responding. Please start the Flask server first.")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to server at {BASE_URL}. Please start the Flask server first.")
        print(f"   Error: {e}")
        sys.exit(1)
    
    print("✅ Server is running")
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Queue status endpoint
    if test_queue_status_endpoint():
        tests_passed += 1
    
    # Test 2: API endpoints
    if test_api_endpoints():
        tests_passed += 1
    
    # Test 3: Concurrent limit
    limit_test_result = test_concurrent_limit()
    if isinstance(limit_test_result, tuple):
        if limit_test_result[0]:
            tests_passed += 1
        session_ids = limit_test_result[1]
    else:
        if limit_test_result:
            tests_passed += 1
        session_ids = []
    
    # Test 4: Queue progression
    if test_queue_progression():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Queue system is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    # Show final queue status
    try:
        final_status = requests.get(f"{BASE_URL}/api/queue-status").json()
        print(f"\n📈 Final queue status:")
        print(f"   Active: {final_status['queue']['active_count']}/{final_status['queue']['max_concurrent']}")
        print(f"   Pending: {final_status['queue']['pending_count']}")
        print(f"   Total sessions: {final_status['sessions']['total_sessions']}")
    except Exception as e:
        print(f"❌ Error getting final status: {e}")

if __name__ == "__main__":
    main()
