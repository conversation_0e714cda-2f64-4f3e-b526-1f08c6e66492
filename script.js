// Global variables
let allRecords = [];
let filteredRecords = [];
let currentPage = 1;
let recordsPerPage = 25;
let sortColumn = '';
let sortDirection = 'asc';

// Tab management
function showTab(tabName, element) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName).classList.add('active');
    if (element) {
        element.classList.add('active');
    }

    // Load data for specific tabs
    if (tabName === 'database') {
        loadTableData();
    }
}

// Utility functions
function showMessage(containerId, message, type = 'info') {
    const container = document.getElementById(containerId);
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

function showLoading(containerId) {
    const container = document.getElementById(containerId);
    container.innerHTML = `
        <div class="loading">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    `;
}

// API functions
async function apiCall(endpoint, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(endpoint, options);
        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Database Table Functions
async function loadTableData() {
    try {
        showTableLoading();
        const result = await apiCall('/api/documents');

        allRecords = result.documents || [];
        filteredRecords = [...allRecords];
        currentPage = 1;

        updateDatabaseInfo();
        displayTable();
        updatePaginationInfo();

        showMessage('database-message', `✅ Loaded ${allRecords.length} records`, 'success');
    } catch (error) {
        showMessage('database-message', `❌ Failed to load records: ${error.message}`, 'error');
        showTableError();
    }
}

function showTableLoading() {
    const tbody = document.getElementById('table-body');
    tbody.innerHTML = `
        <tr>
            <td colspan="4" class="loading-cell">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading records...</p>
                </div>
            </td>
        </tr>
    `;
}

function showTableError() {
    const tbody = document.getElementById('table-body');
    tbody.innerHTML = `
        <tr>
            <td colspan="4" class="loading-cell">
                <p style="color: #dc3545;">❌ Failed to load records</p>
            </td>
        </tr>
    `;
}

function updateDatabaseInfo() {
    document.getElementById('collection-name').textContent = 'my_pdf_collection';
    document.getElementById('total-records').textContent = allRecords.length;
}

function displayTable() {
    const tbody = document.getElementById('table-body');
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const pageRecords = filteredRecords.slice(startIndex, endIndex);

    if (pageRecords.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="loading-cell">
                    <p>No records found</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = pageRecords.map((record, index) => `
        <tr>
            <td class="cell-id" title="${record.id}">${record.id}</td>
            <td class="cell-document" title="${record.document || 'No content'}">${record.document || 'No content'}</td>
            <td class="cell-metadata">
                <div class="metadata-preview" title="${JSON.stringify(record.metadata || {})}">${formatMetadataPreview(record.metadata || {})}</div>
                <button class="btn-small btn-info" onclick="showMetadataModalByIndex(${(currentPage - 1) * recordsPerPage + index})">📋 View Full</button>
            </td>
        </tr>
    `).join('');

    updatePaginationControls();
}

function formatMetadataPreview(metadata) {
    if (!metadata || Object.keys(metadata).length === 0) {
        return '<span class="metadata-empty">No metadata</span>';
    }

    const keys = Object.keys(metadata);
    if (keys.length <= 2) {
        // Show all fields if 2 or fewer
        return keys.map(key => `<span class="metadata-field"><strong>${key}:</strong> ${String(metadata[key]).substring(0, 30)}${String(metadata[key]).length > 30 ? '...' : ''}</span>`).join('<br>');
    } else {
        // Show first 2 fields + count
        const preview = keys.slice(0, 2).map(key => `<span class="metadata-field"><strong>${key}:</strong> ${String(metadata[key]).substring(0, 20)}${String(metadata[key]).length > 20 ? '...' : ''}</span>`).join('<br>');
        return preview + `<br><span class="metadata-more">+${keys.length - 2} more fields</span>`;
    }
}

function showMetadataModalByIndex(recordIndex) {
    const record = filteredRecords[recordIndex];
    if (record) {
        showMetadataModal(record.id, record.metadata || {});
    }
}

function showMetadataModal(recordId, metadata) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('metadata-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'metadata-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content modal-large">
                <div class="modal-header">
                    <h3>📋 Metadata Details</h3>
                    <span class="close" onclick="closeMetadataModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="metadata-info">
                        <strong>Record ID:</strong> <span id="metadata-record-id"></span>
                    </div>
                    <div class="metadata-content">
                        <h4>Metadata Fields:</h4>
                        <div id="metadata-display"></div>
                    </div>
                    <div class="metadata-raw">
                        <h4>Raw JSON:</h4>
                        <pre id="metadata-json"></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="copyMetadataToClipboard()">📋 Copy JSON</button>
                    <button class="btn" onclick="closeMetadataModal()">Close</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Populate modal content
    document.getElementById('metadata-record-id').textContent = recordId;

    // Format metadata fields
    const metadataDisplay = document.getElementById('metadata-display');
    if (!metadata || Object.keys(metadata).length === 0) {
        metadataDisplay.innerHTML = '<p class="metadata-empty">No metadata available</p>';
    } else {
        metadataDisplay.innerHTML = Object.keys(metadata).map(key => `
            <div class="metadata-field-detail">
                <div class="metadata-key">${key}</div>
                <div class="metadata-value">${formatMetadataValue(metadata[key])}</div>
            </div>
        `).join('');
    }

    // Show raw JSON
    document.getElementById('metadata-json').textContent = JSON.stringify(metadata, null, 2);

    // Show modal
    modal.style.display = 'block';
}

function formatMetadataValue(value) {
    if (value === null || value === undefined) {
        return '<span class="metadata-null">null</span>';
    }
    if (typeof value === 'string') {
        return `<span class="metadata-string">"${value}"</span>`;
    }
    if (typeof value === 'number') {
        return `<span class="metadata-number">${value}</span>`;
    }
    if (typeof value === 'boolean') {
        return `<span class="metadata-boolean">${value}</span>`;
    }
    if (typeof value === 'object') {
        return `<span class="metadata-object">${JSON.stringify(value, null, 2)}</span>`;
    }
    return String(value);
}

function closeMetadataModal() {
    const modal = document.getElementById('metadata-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function copyMetadataToClipboard() {
    const jsonText = document.getElementById('metadata-json').textContent;
    navigator.clipboard.writeText(jsonText).then(() => {
        alert('✅ Metadata JSON copied to clipboard!');
    }).catch(() => {
        alert('❌ Failed to copy to clipboard');
    });
}

function updatePaginationInfo() {
    const totalRecords = filteredRecords.length;
    const startRecord = totalRecords === 0 ? 0 : (currentPage - 1) * recordsPerPage + 1;
    const endRecord = Math.min(currentPage * recordsPerPage, totalRecords);

    document.getElementById('pagination-info').textContent =
        `Showing ${startRecord}-${endRecord} of ${totalRecords} records`;
}

function updatePaginationControls() {
    const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);

    document.getElementById('current-page').textContent = currentPage;
    document.getElementById('total-pages').textContent = totalPages;

    document.getElementById('prev-btn').disabled = currentPage <= 1;
    document.getElementById('next-btn').disabled = currentPage >= totalPages;
}

function refreshTable() {
    loadTableData();
}

function filterTable() {
    const query = document.getElementById('table-filter').value.toLowerCase();
    filteredRecords = allRecords.filter(record =>
        record.id.toLowerCase().includes(query) ||
        (record.document && record.document.toLowerCase().includes(query)) ||
        JSON.stringify(record.metadata || {}).toLowerCase().includes(query)
    );
    currentPage = 1;
    displayTable();
    updatePaginationInfo();
}

function sortTable(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    // Update sort indicators
    document.querySelectorAll('.sort-indicator').forEach(indicator => {
        indicator.className = 'sort-indicator';
    });

    const currentIndicator = document.querySelector(`th[onclick="sortTable('${column}')"] .sort-indicator`);
    currentIndicator.className = `sort-indicator ${sortDirection}`;

    // Sort the data
    filteredRecords.sort((a, b) => {
        let aVal = a[column] || '';
        let bVal = b[column] || '';

        if (column === 'metadata') {
            aVal = JSON.stringify(aVal);
            bVal = JSON.stringify(bVal);
        }

        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }

        if (sortDirection === 'asc') {
            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        } else {
            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
        }
    });

    displayTable();
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        displayTable();
        updatePaginationInfo();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        displayTable();
        updatePaginationInfo();
    }
}

function changePageSize() {
    recordsPerPage = parseInt(document.getElementById('page-size').value);
    currentPage = 1;
    displayTable();
    updatePaginationInfo();
}

// Modal Management Functions
function showAddRecordModal() {
    document.getElementById('add-record-modal').style.display = 'block';
    // Clear form
    document.getElementById('modal-doc-id').value = '';
    document.getElementById('modal-doc-content').value = '';
    document.getElementById('modal-doc-metadata').value = '';
    document.getElementById('modal-doc-embedding').value = '';
}

function closeAddRecordModal() {
    document.getElementById('add-record-modal').style.display = 'none';
}

function showEditRecordModal(record) {
    document.getElementById('edit-record-modal').style.display = 'block';
    document.getElementById('edit-doc-id').value = record.id;
    document.getElementById('edit-doc-content').value = record.document || '';
    document.getElementById('edit-doc-metadata').value = JSON.stringify(record.metadata || {}, null, 2);
}

function closeEditRecordModal() {
    document.getElementById('edit-record-modal').style.display = 'none';
}

// Record Management Functions
async function addRecordFromModal() {
    try {
        const id = document.getElementById('modal-doc-id').value || `doc-${Date.now()}`;
        const content = document.getElementById('modal-doc-content').value;
        const metadataStr = document.getElementById('modal-doc-metadata').value;
        const embeddingStr = document.getElementById('modal-doc-embedding').value;

        if (!content.trim()) {
            alert('❌ Document content is required');
            return;
        }

        let metadata = {};
        if (metadataStr.trim()) {
            try {
                metadata = JSON.parse(metadataStr);
            } catch (e) {
                alert('❌ Invalid JSON format in metadata');
                return;
            }
        }

        let embedding = null;
        if (embeddingStr.trim()) {
            try {
                embedding = JSON.parse(embeddingStr);
            } catch (e) {
                alert('❌ Invalid JSON format in embedding');
                return;
            }
        }

        const data = {
            ids: [id],
            documents: [content],
            metadatas: [metadata]
        };

        if (embedding) {
            data.embeddings = [embedding];
        }

        const result = await apiCall('/api/add-document', 'POST', data);

        if (result.status === 'ok') {
            showMessage('database-message', `✅ Record added successfully! ID: ${id}`, 'success');
            closeAddRecordModal();
            loadTableData(); // Refresh table
        } else {
            alert(`❌ Failed to add record: ${result.error}`);
        }
    } catch (error) {
        alert(`❌ Error: ${error.message}`);
    }
}

function generateSampleRecord() {
    const sampleId = `sample-${Date.now()}`;
    const sampleContent = `This is a sample document created at ${new Date().toLocaleString()}. It contains some example text for testing purposes.`;
    const sampleMetadata = JSON.stringify({
        "type": "sample",
        "created": new Date().toISOString(),
        "category": "test"
    }, null, 2);

    document.getElementById('modal-doc-id').value = sampleId;
    document.getElementById('modal-doc-content').value = sampleContent;
    document.getElementById('modal-doc-metadata').value = sampleMetadata;
}

async function editRecord(id) {
    // Find the record
    const record = allRecords.find(r => r.id === id);
    if (!record) {
        alert('Record not found');
        return;
    }

    showEditRecordModal(record);
}

async function updateRecordFromModal() {
    try {
        const id = document.getElementById('edit-doc-id').value;
        const content = document.getElementById('edit-doc-content').value;
        const metadataStr = document.getElementById('edit-doc-metadata').value;

        if (!content.trim()) {
            alert('❌ Document content is required');
            return;
        }

        let metadata = {};
        if (metadataStr.trim()) {
            try {
                metadata = JSON.parse(metadataStr);
            } catch (e) {
                alert('❌ Invalid JSON format in metadata');
                return;
            }
        }

        // First delete the old record
        await apiCall('/api/delete-document', 'POST', { ids: [id] });

        // Then add the updated record
        const data = {
            ids: [id],
            documents: [content],
            metadatas: [metadata]
        };

        const result = await apiCall('/api/add-document', 'POST', data);

        if (result.status === 'ok') {
            showMessage('database-message', `✅ Record updated successfully! ID: ${id}`, 'success');
            closeEditRecordModal();
            loadTableData(); // Refresh table
        } else {
            alert(`❌ Failed to update record: ${result.error}`);
        }
    } catch (error) {
        alert(`❌ Error: ${error.message}`);
    }
}

async function deleteRecord(id) {
    if (!confirm(`Are you sure you want to delete record "${id}"?`)) {
        return;
    }

    try {
        const result = await apiCall('/api/delete-document', 'POST', { ids: [id] });

        if (result.status === 'ok') {
            showMessage('database-message', `✅ Record "${id}" deleted successfully`, 'success');
            loadTableData(); // Refresh table
        } else {
            showMessage('database-message', `❌ Failed to delete record: ${result.error}`, 'error');
        }
    } catch (error) {
        showMessage('database-message', `❌ Error: ${error.message}`, 'error');
    }
}

// Search functions
async function performSearch() {
    try {
        const query = document.getElementById('search-query').value;
        const nResults = parseInt(document.getElementById('search-results-count').value);

        if (!query.trim()) {
            showMessage('search-message', '❌ Please enter a search query', 'error');
            return;
        }

        showLoading('search-results');

        const result = await apiCall('/api/search', 'POST', {
            query: query,
            n_results: nResults
        });

        if (result.documents && result.documents[0]) {
            const documents = result.documents[0].map((doc, index) => ({
                id: result.ids[0][index],
                document: doc,
                metadata: result.metadatas[0][index],
                distance: result.distances ? result.distances[0][index] : null
            }));

            displaySearchResults(documents);
            showMessage('search-message', `✅ Found ${documents.length} results`, 'success');
        } else {
            document.getElementById('search-results').innerHTML = '<div class="loading"><p>No results found</p></div>';
            showMessage('search-message', '📭 No results found', 'info');
        }
    } catch (error) {
        showMessage('search-message', `❌ Search failed: ${error.message}`, 'error');
        document.getElementById('search-results').innerHTML = '';
    }
}

// Store search results globally for metadata modal access
let currentSearchResults = [];

function displaySearchResults(documents) {
    currentSearchResults = documents;
    const container = document.getElementById('search-results');
    container.innerHTML = documents.map((doc, index) => `
        <div class="document-card">
            <div class="document-id">
                ID: ${doc.id}
                ${doc.distance !== null ? `<span style="float: right; color: #6c757d;">Distance: ${doc.distance.toFixed(4)}</span>` : ''}
            </div>
            <div class="document-content">${doc.document || 'No content'}</div>
            <div class="document-metadata">
                <div class="metadata-preview-search">${formatMetadataPreview(doc.metadata || {})}</div>
                <button class="btn-small btn-info" onclick="showSearchMetadataModal(${index})">📋 View Full Metadata</button>
            </div>
        </div>
    `).join('');
}

function showSearchMetadataModal(index) {
    const doc = currentSearchResults[index];
    if (doc) {
        showMetadataModal(doc.id, doc.metadata || {});
    }
}

// PDF Upload functions
function setupFileUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('pdf-file');
    const uploadBtn = document.getElementById('upload-btn');

    // File input change
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            uploadBtn.disabled = false;
            uploadArea.innerHTML = `
                <div>📄</div>
                <h3>File Selected</h3>
                <p>${e.target.files[0].name}</p>
                <button class="btn btn-primary" onclick="document.getElementById('pdf-file').click()">Choose Different File</button>
            `;
        }
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0 && files[0].type === 'application/pdf') {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        } else {
            showMessage('upload-message', '❌ Please drop a PDF file', 'error');
        }
    });

    // Click to select
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
}

async function uploadPDF() {
    try {
        const fileInput = document.getElementById('pdf-file');
        if (!fileInput.files[0]) {
            showMessage('upload-message', '❌ Please select a PDF file', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('pdfFile', fileInput.files[0]);

        showMessage('upload-message', '📤 Uploading and processing PDF...', 'info');

        const response = await fetch('/upload-pdf', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.status === 'success') {
            showMessage('upload-message', `✅ PDF processed successfully! Extracted ${result.elements_count} elements.`, 'success');

            // Reset upload area
            document.getElementById('upload-area').innerHTML = `
                <div>📄</div>
                <h3>Upload PDF File</h3>
                <p>Drag and drop a PDF file here or click to select</p>
                <input type="file" id="pdf-file" accept=".pdf" style="display: none;">
                <button class="btn btn-primary" onclick="document.getElementById('pdf-file').click()">Choose File</button>
            `;
            document.getElementById('upload-btn').disabled = true;
            setupFileUpload();
        } else {
            showMessage('upload-message', `❌ Failed to process PDF: ${result.message}`, 'error');
        }
    } catch (error) {
        showMessage('upload-message', `❌ Upload failed: ${error.message}`, 'error');
    }
}

// Management functions
async function deleteAllDocuments() {
    if (!confirm('⚠️ Are you sure you want to delete ALL documents? This cannot be undone!')) {
        return;
    }

    if (!confirm('🚨 This will permanently delete all data. Are you absolutely sure?')) {
        return;
    }

    try {
        const result = await apiCall('/delete_all', 'POST');

        showMessage('manage-message', result.status, 'error');

        if (result.status === "ok") {
            showMessage('manage-message', `✅ ${result.message}`, 'success');
            refreshTable(); // Refresh table and stats
        } else {
            showMessage('manage-message', `❌ Failed to delete documents: ${result.error}`, 'error');
        }
    } catch (error) {
        showMessage('manage-message', `❌ Error: ${error.message}`, 'error');
    }
}

async function getCollectionInfo() {
    try {
        const result = await apiCall('/api/collection-info');
        showMessage('manage-message', `ℹ️ Collection Info: ${JSON.stringify(result, null, 2)}`, 'info');
    } catch (error) {
        showMessage('manage-message', `❌ Failed to get collection info: ${error.message}`, 'error');
    }
}

async function testConnection() {
    try {
        const result = await apiCall('/api/health');
        showMessage('manage-message', `✅ Connection test successful: ${result.message}`, 'success');
    } catch (error) {
        showMessage('manage-message', `❌ Connection test failed: ${error.message}`, 'error');
    }
}

async function exportData() {
    try {
        const result = await apiCall('/api/export');

        // Create and download file
        const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chromadb-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showMessage('browse-message', '✅ Data exported successfully!', 'success');
    } catch (error) {
        showMessage('browse-message', `❌ Export failed: ${error.message}`, 'error');
    }
}

async function exportData() {
    try {
        const result = await apiCall('/api/export');

        // Create and download file
        const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chromadb-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showMessage('database-message', '✅ Data exported successfully!', 'success');
    } catch (error) {
        showMessage('database-message', `❌ Export failed: ${error.message}`, 'error');
    }
}

// Close modals when clicking outside
window.onclick = function(event) {
    const addModal = document.getElementById('add-record-modal');
    const editModal = document.getElementById('edit-record-modal');

    if (event.target === addModal) {
        closeAddRecordModal();
    }
    if (event.target === editModal) {
        closeEditRecordModal();
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupFileUpload();
    loadTableData();
});