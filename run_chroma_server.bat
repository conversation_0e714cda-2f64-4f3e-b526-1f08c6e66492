@echo off
REM ChromaDB Server Startup Script
REM This script runs the ChromaDB server in a virtual environment with hidden console

REM Change to the project directory
cd /d "c:\code\AI_Agent_Project_Improved\chroma_server"

REM Activate the virtual environment and run the main.py script
REM Using pythonw.exe to run without console window
venv\Scripts\pythonw.exe main.py

REM If pythonw fails, fallback to regular python (this won't be reached if pythonw succeeds)
REM venv\Scripts\python.exe main.py
