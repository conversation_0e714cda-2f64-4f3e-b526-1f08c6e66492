"""
Web search routes for the ChromaDB server.
Provides endpoints for web search functionality using SearXNG.
"""

import logging
from flask import request, jsonify
from src.core.search_tool import web_search_and_fetch, WebSearchError, ContentFetchError

logger = logging.getLogger(__name__)


def create_error_response(message, status_code=500, error_type="error"):
    """Create standardized error response."""
    return jsonify({
        "status": "error",
        "error_type": error_type,
        "message": message
    }), status_code


def create_success_response(data=None, message="Success"):
    """Create standardized success response."""
    response = {"status": "success", "message": message}
    if data:
        response.update(data)
    return jsonify(response)


def web_search(app):
    """
    Handle web search requests - search query and fetch content from top results.
    
    Expected JSON payload:
    {
        "query": "search query string",
        "num_results": 3  // optional, defaults to 3
    }
    
    Returns:
    {
        "status": "success",
        "query": "original query",
        "message": "processing summary",
        "results": [
            {
                "search_title": "Title from search results",
                "fetched_title": "Title from actual webpage",
                "url": "https://example.com",
                "snippet": "Search result snippet",
                "content": "Full webpage content",
                "content_length": 1234,
                "fetch_status": "success"
            }
        ],
        "total_results": 3,
        "successful_fetches": 2,
        "processing_time": 5.67
    }
    """
    try:
        # Validate request
        if not request.is_json:
            return create_error_response(
                "Request must be JSON",
                400,
                "validation_error"
            )
        
        data = request.get_json()
        if not data:
            return create_error_response(
                "No JSON data provided",
                400,
                "validation_error"
            )

        # Log the request body for debugging
        app.logger.info(f"Web search request body: {data}")

        # Extract parameters
        query = data.get('query')
        num_results = data.get('num_results', 3)
        
        # Validate query
        if not query:
            return create_error_response(
                "Query parameter is required",
                400,
                "validation_error"
            )
        
        if not isinstance(query, str) or not query.strip():
            return create_error_response(
                "Query must be a non-empty string",
                400,
                "validation_error"
            )
        
        # Validate and convert num_results
        try:
            num_results = int(num_results)
            if num_results < 1 or num_results > 10:
                return create_error_response(
                    "num_results must be an integer between 1 and 10",
                    400,
                    "validation_error"
                )
        except (ValueError, TypeError):
            return create_error_response(
                "num_results must be a valid integer between 1 and 10",
                400,
                "validation_error"
            )
        
        app.logger.info(f"Web search request: query='{query}', num_results={num_results}")
        
        # Perform web search and content fetching
        try:
            result = web_search_and_fetch(query, num_results)
            
            app.logger.info(f"Web search completed successfully: {result['total_results']} results, "
                          f"{result['successful_fetches']} successful fetches")
            
            return jsonify(result), 200
            
        except WebSearchError as e:
            app.logger.error(f"Web search error: {str(e)}")
            return create_error_response(
                str(e),
                503,  # Service Unavailable
                "search_error"
            )
        
        except ContentFetchError as e:
            app.logger.error(f"Content fetch error: {str(e)}")
            return create_error_response(
                str(e),
                502,  # Bad Gateway
                "fetch_error"
            )
    
    except Exception as e:
        app.logger.error(f"Unexpected error in web search endpoint: {str(e)}")
        return create_error_response(
            "Internal server error occurred during web search",
            500,
            "internal_error"
        )


def web_search_health_check(app):
    """
    Health check endpoint for web search functionality.
    Tests connectivity to SearXNG service.
    
    Returns:
    {
        "status": "success",
        "message": "Web search service is healthy",
        "searxng_url": "http://localhost:8888",
        "searxng_status": "connected"
    }
    """
    try:
        import requests
        from src.core.search_tool import SEARXNG_BASE_URL, SEARCH_TIMEOUT
        
        app.logger.info("Performing web search health check")
        
        # Test SearXNG connectivity
        try:
            response = requests.get(
                f"{SEARXNG_BASE_URL}/",
                timeout=5  # Short timeout for health check
            )
            
            if response.status_code == 200:
                searxng_status = "connected"
                searxng_message = "SearXNG is responding"
            else:
                searxng_status = "error"
                searxng_message = f"SearXNG returned status {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            searxng_status = "disconnected"
            searxng_message = "Cannot connect to SearXNG service"
        except requests.exceptions.Timeout:
            searxng_status = "timeout"
            searxng_message = "SearXNG connection timed out"
        except Exception as e:
            searxng_status = "error"
            searxng_message = f"SearXNG check failed: {str(e)}"
        
        # Determine overall health
        if searxng_status == "connected":
            overall_status = "healthy"
            overall_message = "Web search service is healthy"
            status_code = 200
        else:
            overall_status = "unhealthy"
            overall_message = f"Web search service is unhealthy: {searxng_message}"
            status_code = 503
        
        app.logger.info(f"Web search health check completed: {overall_status}")
        
        return jsonify({
            "status": "success" if overall_status == "healthy" else "error",
            "message": overall_message,
            "service_status": overall_status,
            "searxng_url": SEARXNG_BASE_URL,
            "searxng_status": searxng_status,
            "searxng_message": searxng_message,
            "search_timeout": SEARCH_TIMEOUT
        }), status_code
        
    except Exception as e:
        app.logger.error(f"Error in web search health check: {str(e)}")
        return create_error_response(
            "Health check failed",
            500,
            "health_check_error"
        )
