<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChromaDB Management Interface</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ ChromaDB Management</h1>
            <p>Manage your vector database with ease</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('database', this)">🗄️ Database</button>
            <button class="nav-tab" onclick="showTab('search', this)">🔍 Search</button>
            <button class="nav-tab" onclick="showTab('manage', this)">⚙️ Manage</button>
        </div>

        <!-- Database Tab -->
        <div id="database" class="tab-content active">
            <!-- Database Info Header -->
            <div class="database-header">
                <div class="database-info">
                    <h3>📊 Database: <span id="db-name">ChromaDB</span></h3>
                    <p>Collection: <span id="collection-name">my_pdf_collection</span> | Records: <span id="total-records">0</span></p>
                </div>
                <div class="database-actions">
                    <button class="btn btn-primary" onclick="refreshTable()">🔄 Refresh</button>
                    <button class="btn btn-success" onclick="showAddRecordModal()">➕ Add Record</button>
                    <button class="btn btn-success" onclick="exportData()">💾 Export</button>
                </div>
            </div>

            <!-- Table Controls -->
            <div class="table-controls">
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input type="text" id="table-filter" placeholder="Filter records..." onkeyup="filterTable()">
                </div>
                <div class="table-pagination-info">
                    <span id="pagination-info">Showing 0 of 0 records</span>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-container">
                <table class="data-table" id="records-table">
                    <thead>
                        <tr>
                            <th class="sortable" onclick="sortTable('id')">ID <span class="sort-indicator"></span></th>
                            <th class="sortable" onclick="sortTable('document')">Document <span class="sort-indicator"></span></th>
                            <th class="sortable" onclick="sortTable('metadata')">Metadata <span class="sort-indicator"></span></th>
                        </tr>
                    </thead>
                    <tbody id="table-body">
                        <tr>
                            <td colspan="4" class="loading-cell">
                                <div class="loading">
                                    <div class="spinner"></div>
                                    <p>Loading records...</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Table Pagination -->
            <div class="table-pagination">
                <div class="pagination-controls">
                    <button class="btn" onclick="previousPage()" id="prev-btn" disabled>← Previous</button>
                    <span class="page-info">
                        Page <span id="current-page">1</span> of <span id="total-pages">1</span>
                    </span>
                    <button class="btn" onclick="nextPage()" id="next-btn" disabled>Next →</button>
                </div>
                <div class="page-size-control">
                    <label for="page-size">Records per page:</label>
                    <select id="page-size" onchange="changePageSize()">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>

            <div id="database-message"></div>
        </div>

        <!-- Search Tab -->
        <div id="search" class="tab-content">
            <div class="form-group">
                <label for="search-query">Search Query:</label>
                <input type="text" id="search-query" placeholder="Enter search terms...">
            </div>

            <div class="form-group">
                <label for="search-results-count">Number of Results:</label>
                <select id="search-results-count">
                    <option value="5">5</option>
                    <option value="10" selected>10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                </select>
            </div>

            <div class="form-group">
                <button class="btn btn-primary" onclick="performSearch()">🔍 Search</button>
            </div>

            <div id="search-message"></div>
            <div id="search-results" class="documents-grid"></div>
        </div>

        <!-- Upload PDF Tab -->
        <div id="upload" class="tab-content">
            <div class="file-upload-area" id="upload-area">
                <div>📄</div>
                <h3>Upload PDF File</h3>
                <p>Drag and drop a PDF file here or click to select</p>
                <input type="file" id="pdf-file" accept=".pdf" style="display: none;">
                <button class="btn btn-primary" onclick="document.getElementById('pdf-file').click()">Choose File</button>
            </div>

            <div class="form-group">
                <button class="btn btn-success" onclick="uploadPDF()" id="upload-btn" disabled>📤 Upload & Process</button>
            </div>

            <div id="upload-message"></div>
        </div>

        <!-- Manage Tab -->
        <div id="manage" class="tab-content">
            <div class="alert alert-info">
                <strong>⚠️ Warning:</strong> These operations cannot be undone. Please use with caution.
            </div>

            <div class="form-group">
                <h3>🗑️ Delete Operations</h3>
                <button class="btn btn-danger" onclick="deleteAllDocuments()">🗑️ Delete All Documents</button>
            </div>

            <div class="form-group">
                <h3>🔧 Maintenance</h3>
                <button class="btn btn-primary" onclick="getCollectionInfo()">ℹ️ Collection Info</button>
                <button class="btn btn-success" onclick="testConnection()">🔗 Test Connection</button>
            </div>

            <div id="manage-message"></div>
        </div>
    </div>

    <!-- Add Record Modal -->
    <div id="add-record-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>➕ Add New Record</h3>
                <span class="close" onclick="closeAddRecordModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="modal-doc-id">Document ID:</label>
                    <input type="text" id="modal-doc-id" placeholder="Leave empty for auto-generation">
                </div>

                <div class="form-group">
                    <label for="modal-doc-content">Document Content:</label>
                    <textarea id="modal-doc-content" rows="6" placeholder="Enter document content here..."></textarea>
                </div>

                <div class="form-group">
                    <label for="modal-doc-metadata">Metadata (JSON format):</label>
                    <textarea id="modal-doc-metadata" rows="3" placeholder='{"key": "value", "category": "example"}'></textarea>
                </div>

                <div class="form-group">
                    <label for="modal-doc-embedding">Embedding (optional - will be generated if empty):</label>
                    <textarea id="modal-doc-embedding" rows="3" placeholder="[0.1, 0.2, 0.3, ...]"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="addRecordFromModal()">➕ Add Record</button>
                <button class="btn btn-success" onclick="generateSampleRecord()">🎲 Generate Sample</button>
                <button class="btn" onclick="closeAddRecordModal()">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Edit Record Modal -->
    <div id="edit-record-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ Edit Record</h3>
                <span class="close" onclick="closeEditRecordModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="edit-doc-id">Document ID:</label>
                    <input type="text" id="edit-doc-id" readonly>
                </div>

                <div class="form-group">
                    <label for="edit-doc-content">Document Content:</label>
                    <textarea id="edit-doc-content" rows="6"></textarea>
                </div>

                <div class="form-group">
                    <label for="edit-doc-metadata">Metadata (JSON format):</label>
                    <textarea id="edit-doc-metadata" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="updateRecordFromModal()">💾 Update Record</button>
                <button class="btn" onclick="closeEditRecordModal()">Cancel</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>