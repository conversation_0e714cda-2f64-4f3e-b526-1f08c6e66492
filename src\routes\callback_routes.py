"""
Callback routes for handling N8N workflow responses and chunk processing.
"""

from datetime import datetime
from flask import request, jsonify

from ..core.state import (
    queue_lock, processing_status, chunk_queues, plan_b_sessions,
    state_manager, UploadStatus
)
from ..flows.process_queue import queue_manager
from ..core.config import GPU_REST_DELAY
from ..flows.management import _send_next_chunk_with_delay, get_chunk_status, get_plan_b_status
from ..flows.n8n_integration import _send_chunk_to_onfail_webhook
from ..core.utils import send_upload_status_notification


def chunk_processing_complete(app):
    """Handle chunk processing completion callback from n8n."""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        status = data.get('status', 'success')
        chunk_id = data.get('chunk_id')
        plan_b_completed = data.get('plan_b_completed', False)
        app.logger.info(f"Received chunk completion callback: session_id={session_id}, chunk_id={chunk_id}, status={status}, plan_b_completed={plan_b_completed}")

        # Check if this is a Plan B session that should be redirected
        if session_id and session_id.startswith('planb_'):
            app.logger.info(f"Redirecting Plan B session {session_id} to plan_b_piece_complete endpoint")
            # Forward to Plan B piece complete handler
            return plan_b_piece_complete(app)

        if not session_id:
            app.logger.warning("No session_id provided in chunk completion callback")
            return jsonify({"status": "error", "message": "session_id required"}), 400

        with queue_lock:
            if session_id not in processing_status:
                app.logger.warning(f"Session {session_id} not found in processing_status")
                return jsonify({"status": "error", "message": "Session not found"}), 404

            # Update processing status
            processing_status[session_id]['last_completed'] = chunk_id
            processing_status[session_id]['last_status'] = status
            processing_status[session_id]['last_update'] = datetime.now()
            processing_status[session_id]['processed_chunks'] = processing_status[session_id].get('processed_chunks', 0) + 1

            # Update queue manager and state manager with progress
            completed_chunks = processing_status[session_id]['processed_chunks']
            queue_manager.update_chunk_progress(session_id, completed_chunks)
            state_manager.update_chunk_progress(session_id, completed_chunks)

            # Special handling for Plan B completion
            if plan_b_completed:
                app.logger.info(f"Plan B completion detected for session {session_id}, chunk {chunk_id}. Continuing with next chunk.")

            if status != 'success':
                processing_status[session_id]['error'] = f"Chunk {chunk_id} failed with status: {status}"
                processing_status[session_id]['status'] = 'error'
                app.logger.error(f"Chunk processing failed for session {session_id}, chunk {chunk_id}: {status}")

                # Update queue manager and state manager with error
                error_message = f"Chunk {chunk_id} failed with status: {status}"
                queue_manager.mark_process_completed(session_id, success=False, error_message=error_message)
                state_manager.update_session_status(session_id, UploadStatus.FAILED, error_message, error_message=error_message)

                # Notify frontend of error
                try:
                    send_upload_status_notification({
                        "endpoint_type": "pdf",
                        "status": "error",
                        "filename": processing_status[session_id].get('filename'),
                        "session_id": session_id,
                        "message": f"Error processing {processing_status[session_id].get('filename')}",
                        "error": f"{status} at {chunk_id}"
                    })
                except Exception:
                    pass
                return jsonify({"status": "error", "message": f"Chunk processing failed: {status}"}), 500

            # Check if there are more chunks to process
            app.logger.info(f"Checking for more chunks. Session {session_id} in chunk_queues: {session_id in chunk_queues}")
            if session_id in chunk_queues:
                app.logger.info(f"Chunk queue for session {session_id} has {len(chunk_queues[session_id])} chunks remaining")

            if session_id in chunk_queues and chunk_queues[session_id]:
                # Get next chunk from queue
                next_chunk_info = chunk_queues[session_id].pop(0)
                app.logger.info(f"Retrieved next chunk: {next_chunk_info[1] if len(next_chunk_info) > 1 else 'unknown'}")
                
                # If queue is now empty, remove it
                if not chunk_queues[session_id]:
                    del chunk_queues[session_id]
                
                app.logger.info(f"Scheduling next chunk for session {session_id} with {GPU_REST_DELAY}s delay")
                
                # Send next chunk with delay
                _send_next_chunk_with_delay(session_id, next_chunk_info, GPU_REST_DELAY)
                
                return jsonify({
                    "status": "success", 
                    "message": f"Chunk {chunk_id} completed, next chunk scheduled",
                    "next_chunk": next_chunk_info[1]
                })
            else:
                # No more chunks, mark session as complete
                processing_status[session_id]['status'] = 'completed'
                app.logger.info(f"All chunks completed for session {session_id}. Total processed: {completed_chunks}")

                # Update queue manager and state manager
                queue_manager.mark_process_completed(session_id, success=True)
                state_manager.update_session_status(session_id, UploadStatus.COMPLETED, "Processing completed successfully")

                # Notify frontend of completion (no chunk details in UI)
                try:
                    _fname = processing_status[session_id].get('filename')
                    _etype = 'youtube' if (_fname and _fname.endswith('.yt')) else 'pdf'
                    send_upload_status_notification({
                        "endpoint_type": _etype,
                        "status": "completed",
                        "filename": _fname,
                        "session_id": session_id,
                        "message": f"Completed processing {_fname}"
                    })
                except Exception:
                    pass

                return jsonify({
                    "status": "success",
                    "message": f"All chunks completed for session {session_id}",
                    "total_processed": processing_status[session_id]['processed_chunks']
                })

    except Exception as e:
        app.logger.error(f"Error in chunk_processing_complete: {str(e)}")
        return jsonify({"status": "error", "message": "Internal server error"}), 500


def plan_b_piece_complete(app):
    """Handle Plan B piece completion callback from OnFail workflow."""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        status = data.get('status', 'success')
        piece_number = data.get('plan_b_piece', 1)

        app.logger.info(f"Received Plan B piece completion: session_id={session_id}, piece={piece_number}, status={status}")

        if not session_id:
            return jsonify({"status": "error", "message": "session_id required"}), 400

        # Debug: Log current Plan B sessions
        with queue_lock:
            app.logger.info(f"Current Plan B sessions: {list(plan_b_sessions.keys())}")
            app.logger.info(f"Looking for session: {session_id}")

            if session_id not in plan_b_sessions:
                app.logger.warning(f"Plan B session {session_id} not found")
                app.logger.info(f"Available sessions: {list(plan_b_sessions.keys())}")
                return jsonify({"status": "error", "message": "Plan B session not found"}), 404

            plan_b_session = plan_b_sessions[session_id]
            
            # Update completion status
            plan_b_session['pieces_completed'] += 1
            
            if status != 'success':
                plan_b_session['status'] = 'error'
                plan_b_session['error'] = f"Piece {piece_number} failed with status: {status}"
                app.logger.error(f"Plan B piece {piece_number} failed for session {session_id}: {status}")
                return jsonify({"status": "error", "message": f"Plan B piece processing failed: {status}"}), 500

            # Check if we need to send the next piece
            total_pieces = plan_b_session['total_pieces']
            pieces_sent = plan_b_session['pieces_sent']
            
            if pieces_sent < total_pieces:
                # Send next piece
                next_piece_number = pieces_sent + 1
                
                if next_piece_number == 2 and plan_b_session.get('second_piece'):
                    piece_text = plan_b_session['second_piece']
                elif next_piece_number == 3 and plan_b_session.get('third_piece'):
                    piece_text = plan_b_session['third_piece']
                else:
                    app.logger.error(f"No piece {next_piece_number} available for session {session_id}")
                    plan_b_session['status'] = 'error'
                    plan_b_session['error'] = f"No piece {next_piece_number} available"
                    return jsonify({"status": "error", "message": f"No piece {next_piece_number} available"}), 500

                # Prepare session data for next piece
                session_data = {
                    'session_id': session_id,
                    'chunk_number': f"{plan_b_session['original_chunk_number']}_piece_{next_piece_number}",
                    'source': plan_b_session['source']
                }

                success = _send_chunk_to_onfail_webhook(piece_text, session_data, next_piece_number)
                
                if success:
                    plan_b_session['pieces_sent'] = next_piece_number
                    app.logger.info(f"Plan B piece {next_piece_number} sent successfully for session {session_id}")
                    
                    return jsonify({
                        "status": "success",
                        "message": f"Piece {piece_number} completed, piece {next_piece_number} sent",
                        "pieces_completed": plan_b_session['pieces_completed'],
                        "pieces_sent": plan_b_session['pieces_sent'],
                        "total_pieces": total_pieces
                    })
                else:
                    plan_b_session['status'] = 'error'
                    plan_b_session['error'] = f"Failed to send piece {next_piece_number}"
                    app.logger.error(f"Failed to send Plan B piece {next_piece_number} for session {session_id}")
                    return jsonify({"status": "error", "message": f"Failed to send piece {next_piece_number}"}), 500
            else:
                # All pieces have been sent, check if all are completed
                if plan_b_session['pieces_completed'] >= total_pieces:
                    plan_b_session['status'] = 'completed'
                    app.logger.info(f"All Plan B pieces completed for session {session_id}")

                    # Trigger continuation of original workflow to process next chunk
                    original_session_id = plan_b_session['original_session_id']
                    original_chunk_number = plan_b_session['original_chunk_number']

                    # Mark the original session as completed in the queue system
                    # This will trigger the next queued process to start
                    app.logger.info(f"Marking original session {original_session_id} as completed in queue system")
                    queue_manager.mark_process_completed(original_session_id, success=True)
                    state_manager.update_session_status(original_session_id, UploadStatus.COMPLETED, "Plan B processing completed successfully")

                    app.logger.info(f"Plan B processing completed for session {session_id} ({total_pieces} pieces), triggering continuation")

                    def trigger_workflow_continuation():
                        import time
                        import requests
                        try:
                            time.sleep(1)  # Small delay before triggering continuation

                            # Call chunk_complete endpoint to continue original workflow
                            continuation_payload = {
                                "session_id": original_session_id,
                                "chunk_id": original_chunk_number,
                                "status": "success",
                                "plan_b_completed": True,
                                "plan_b_pieces": total_pieces
                            }

                            response = requests.post(
                                "http://localhost:5555/chunk_complete",
                                json=continuation_payload,
                                headers={'Content-Type': 'application/json'},
                                timeout=30
                            )

                            if response.status_code == 200:
                                app.logger.info(f"Successfully triggered continuation for original session {original_session_id}")
                            else:
                                app.logger.error(f"Failed to trigger continuation: {response.status_code}")

                        except Exception as e:
                            app.logger.error(f"Error triggering workflow continuation: {str(e)}")

                    # Start thread to trigger continuation
                    import threading
                    threading.Thread(target=trigger_workflow_continuation, daemon=True).start()

                    return jsonify({
                        "status": "success",
                        "message": f"All Plan B pieces completed for session {session_id}",
                        "pieces_completed": plan_b_session['pieces_completed'],
                        "total_pieces": total_pieces
                    })
                else:
                    # Waiting for more pieces to complete
                    return jsonify({
                        "status": "success",
                        "message": f"Piece {piece_number} completed, waiting for remaining pieces",
                        "pieces_completed": plan_b_session['pieces_completed'],
                        "total_pieces": total_pieces
                    })

    except Exception as e:
        app.logger.error(f"Error in plan_b_piece_complete: {str(e)}")
        return jsonify({"status": "error", "message": "Internal server error"}), 500


def get_chunk_status_route(session_id, app):
    """Get the current status of chunk processing for a session."""
    try:
        status = get_chunk_status(session_id)
        if status:
            return jsonify({"status": "success", "session_status": status})
        else:
            return jsonify({"status": "error", "message": "Session not found"}), 404
    except Exception as e:
        app.logger.error(f"Error getting chunk status: {str(e)}")
        return jsonify({"status": "error", "message": "Internal server error"}), 500


def get_plan_b_status_route(session_id, app):
    """Get the current status of Plan B processing for a session."""
    try:
        status = get_plan_b_status(session_id)
        if status:
            return jsonify({"status": "success", "session_status": status})
        else:
            return jsonify({"status": "error", "message": "Plan B session not found"}), 404
    except Exception as e:
        app.logger.error(f"Error getting Plan B status: {str(e)}")
        return jsonify({"status": "error", "message": "Internal server error"}), 500
