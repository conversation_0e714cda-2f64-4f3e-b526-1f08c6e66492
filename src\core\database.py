"""
ChromaDB client setup and configuration.
"""

import chromadb
from chromadb.config import Settings
from .config import COLLECTION_NAME


# ChromaDB setup
client = chromadb.PersistentClient(
    path="./chroma_data",
    settings=Settings(
        chroma_client_auth_provider="chromadb.auth.token.TokenAuthClientProvider",
        chroma_client_auth_credentials="your-token-here"
    )
)

collection = client.get_or_create_collection(name=COLLECTION_NAME)
