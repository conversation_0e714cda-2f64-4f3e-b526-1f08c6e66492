# API Endpoints and Workflow Documentation

Notes: docker run -it --rm --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n n8nio/n8n

This document describes the API endpoints and the complete workflow of the ChromaDB server system.

## 🚀 **API Endpoints**

### **Base URL**
```
http://localhost:8000
```

### **1. Health Check**
```http
GET /health
```

**Response:**
```json
{
    "status": "healthy",
    "timestamp": "2025-01-08T12:00:00Z",
    "version": "1.0.0"
}
```

### **2. Upload Documents**
```http
POST /upload-pdf
```

**Request Body (multipart/form-data):**
- `pdfFile`: PDF document file
- `source` (optional): Source identifier

**Response:**
```json
{
    "status": "success",
    "message": "PDF processed successfully",
    "data": {
        "filename": "document.pdf",
        "estimated_tokens": 1250,
        "chunk_count": 3,
        "workflow_triggered": true,
        "processing_method": "pdf_extraction"
    }
}
```

### **3. Upload YouTube Transcript**
```http
POST /upload-youtube-transcript
```

**Request Body (JSON):**
```json
{
    "transcript_text": "This is the full transcript text from YouTube...",
    "video_title": "How to Build AI Applications",
    "video_url": "https://youtube.com/watch?v=example",
    "source": "youtube_transcripts"
}
```

**Response:**
```json
{
    "status": "success",
    "message": "YouTube transcript processed successfully",
    "data": {
        "video_title": "How to Build AI Applications",
        "video_url": "https://youtube.com/watch?v=example",
        "source": "youtube_transcripts",
        "transcript_length": 15420,
        "estimated_tokens": 3855,
        "chunk_count": 4,
        "workflow_triggered": true,
        "processing_method": "youtube_transcript"
    }
}
```

### **4. Query Documents**
```http
POST /query
```

**Request Body (JSON):**
```json
{
    "message": "What is the financial performance?",
    "source": "uploaded_docs",
    "max_results": 50
}
```

**Response:**
```json
{
    "response": "Based on the documents, the financial performance shows...",
    "sources": [
        {
            "title": "Financial Report Q4",
            "content": "The company reported strong performance...",
            "source": "uploaded_docs",
            "relevance_score": 0.95
        }
    ],
    "total_chunks_found": 36,
    "processing_time_ms": 1250
}
```

### **5. List Sources**
```http
GET /sources
```

**Response:**
```json
{
    "sources": [
        {
            "name": "uploaded_docs",
            "document_count": 12,
            "chunk_count": 456,
            "last_updated": "2025-01-08T11:30:00Z"
        }
    ]
}
```

### **6. Delete Source**
```http
DELETE /sources/{source_name}
```

**Response:**
```json
{
    "message": "Source 'uploaded_docs' deleted successfully",
    "chunks_deleted": 456
}
```

## 🔄 **Complete Workflow**

### **Phase 1: Document Upload and Processing**

#### **PDF Upload Flow**
```mermaid
graph TD
    A[User uploads PDF] --> B[File validation]
    B --> C[PDF text extraction]
    C --> D[Text chunking]
    D --> E[Generate embeddings]
    E --> F[Store in ChromaDB]
    F --> G[Return success response]
```

#### **YouTube Transcript Upload Flow**
```mermaid
graph TD
    A[User uploads transcript] --> B[JSON validation]
    B --> C[Text chunking]
    C --> D[Generate embeddings]
    D --> E[Store in ChromaDB]
    E --> F[Return success response]
```

**Detailed Steps:**

#### **PDF Upload** (`POST /upload-pdf`)
1. **File Upload**
   - Receive PDF file via multipart form data
   - Validate file type and size
   - Save to temporary location

2. **Text Extraction**
   - Use unstructured library for PDF parsing
   - Extract text, tables, and metadata
   - Handle complex layouts and formatting

3. **Text Chunking**
   - Split text into manageable chunks (~500-1000 characters)
   - Preserve sentence boundaries
   - Maintain context overlap between chunks

4. **Processing Pipeline**
   - Generate embeddings via n8n workflow
   - Store in ChromaDB with metadata
   - Clean up temporary files

#### **YouTube Transcript Upload** (`POST /upload-youtube-transcript`)
1. **JSON Validation**
   - Validate required fields (transcript_text)
   - Check text length limits (50 - 1,000,000 chars)
   - Extract video metadata (title, URL, source)

2. **Text Chunking** (Same as PDF)
   - Split transcript into manageable chunks
   - Preserve sentence boundaries
   - Maintain context overlap

3. **Processing Pipeline** (Same as PDF)
   - Generate embeddings via n8n workflow
   - Store in ChromaDB with video metadata
   - No file cleanup needed (text-only)

### **Phase 2: Query Processing and Response**

```mermaid
graph TD
    A[User query received] --> B[Extract keywords]
    B --> C[Generate query embedding]
    C --> D[Keyword filtering]
    D --> E[Embedding similarity search]
    E --> F[Combine results]
    F --> G[Send to AI model]
    G --> H[Generate response]
    H --> I[Return formatted response]
```

**Detailed Steps:**

1. **Query Reception** (`POST /query`)
   - Receive user query and parameters
   - Validate input and source filter

2. **Keyword Extraction**
   - Use AI model (GPT/Qwen) to extract key terms
   - Filter out stop words and common terms
   - Generate search keywords

3. **Dual Search Strategy**
   - **Keyword Search**: Find chunks containing exact keywords
   - **Embedding Search**: Find semantically similar chunks
   - Combine both approaches for comprehensive results

4. **Result Processing**
   - Remove duplicates between keyword and embedding results
   - Rank by relevance (distance-based)
   - Limit to requested number of results

5. **AI Response Generation**
   - Send combined chunks to AI model as context
   - Generate natural language response
   - Include source citations

6. **Response Formatting**
   - Format response with sources
   - Include metadata and relevance scores
   - Return processing time statistics

## 🏗️ **System Architecture**

### **Core Components**

1. **FastAPI Server** (`src/main.py`)
   - HTTP endpoint handling
   - Request/response processing
   - Error handling and logging

2. **Document Processing** (`src/flows/upload.py`)
   - File upload handling
   - Text extraction and chunking
   - Embedding generation

3. **Query Engine** (`src/flows/query.py`)
   - Keyword extraction
   - Dual search implementation
   - Result combination and ranking

4. **Database Layer** (`src/core/database.py`)
   - ChromaDB integration
   - Vector storage and retrieval
   - Metadata management

5. **Configuration** (`src/core/config.py`)
   - System settings
   - Model configurations
   - Performance tuning parameters

### **Data Flow**

```
User Request → FastAPI → Processing Layer → ChromaDB → AI Model → Response
```

### **Storage Structure**

**ChromaDB Collections:**
- **Documents**: Original document metadata
- **Chunks**: Text chunks with embeddings
- **Sources**: Source-based organization

**Metadata Schema:**
```json
{
    "Title": "Document title or section header",
    "Remark": "Additional context or description", 
    "Source": "Source identifier",
    "ChunkIndex": "Position within document",
    "DocumentId": "Original document identifier"
}
```

## ⚙️ **Configuration Options**

### **Performance Settings**
```python
# Chunk processing
CHUNK_SIZE = 1000  # Characters per chunk
CHUNK_OVERLAP = 200  # Overlap between chunks

# Search parameters
KEYWORD_CHUNKS = 20  # Max keyword search results
EMBEDDING_CHUNKS = 30  # Max embedding search results
MAX_TOTAL_RESULTS = 50  # Max combined results

# AI model settings
KEYWORD_EXTRACTION_MODEL = "gpt-oss:20b"
RESPONSE_GENERATION_MODEL = "qwen3:32b"
```

### **Embedding Model**
```python
EMBEDDING_MODEL = "all-MiniLM-L6-v2"
EMBEDDING_DIMENSIONS = 384
```

## 🔧 **Development and Deployment**

### **Local Development**
```bash
# Install dependencies
pip install -r requirements.txt

# Start ChromaDB
# (Embedded mode - no separate server needed)

# Start FastAPI server
python src/main.py

# Server runs on http://localhost:8000
```

### **Production Deployment**
```bash
# Use production ASGI server
uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 4

# Or with Gunicorn
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### **Environment Variables**
```bash
CHROMA_DB_PATH=/path/to/chroma/db
OLLAMA_BASE_URL=http://localhost:11434
LOG_LEVEL=INFO
MAX_FILE_SIZE_MB=100
```

## 📊 **Performance Characteristics**

### **Typical Response Times**
- **Document Upload**: 2-10 seconds (depending on size)
- **Simple Query**: 500-2000ms
- **Complex Query**: 1-5 seconds
- **Embedding Generation**: ~100ms per chunk

### **Scalability**
- **Documents**: Tested up to 10,000 documents
- **Chunks**: Handles 100,000+ chunks efficiently
- **Concurrent Users**: 10-50 (depending on hardware)
- **Memory Usage**: 2-8GB (depending on model and data size)

## 🚨 **Error Handling**

### **Common Error Responses**
```json
{
    "error": "File too large",
    "code": "FILE_SIZE_EXCEEDED",
    "max_size_mb": 100
}
```

```json
{
    "error": "Source not found",
    "code": "SOURCE_NOT_FOUND",
    "available_sources": ["uploaded_docs", "manual_docs"]
}
```

### **HTTP Status Codes**
- `200`: Success
- `400`: Bad Request (invalid input)
- `404`: Not Found (source/document not found)
- `413`: Payload Too Large (file size exceeded)
- `500`: Internal Server Error

## 📝 **Usage Examples**

### **Upload a PDF Document**
```bash
curl -X POST "http://localhost:8000/upload-pdf" \
  -F "pdfFile=@document.pdf"
```

### **Upload YouTube Transcript**
```bash
curl -X POST "http://localhost:8000/upload-youtube-transcript" \
  -H "Content-Type: application/json" \
  -d '{
    "transcript_text": "Welcome to this tutorial on AI development. In this video, we will cover the basics of machine learning and how to build your first AI model...",
    "video_title": "AI Development Tutorial - Getting Started",
    "video_url": "https://youtube.com/watch?v=abc123",
    "source": "youtube_tutorials"
  }'
```

### **Query Documents**
```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the key findings?",
    "source": "my_docs",
    "max_results": 20
  }'
```

### **List All Sources**
```bash
curl -X GET "http://localhost:8000/sources"
```

This system provides a complete document processing and querying solution with efficient vector search, AI-powered responses, and a clean REST API interface.
