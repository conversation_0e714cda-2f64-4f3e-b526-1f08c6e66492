"""
Enhanced State Management for Process Queue System.

This module provides centralized state management for tracking active processes,
pending queue, session metadata, and upload status notifications.
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)

# Legacy state management (kept for backward compatibility)
chunk_queues = {}
chunk_sessions = {}
processing_status = {}
plan_b_sessions = {}  # Track Plan B split processing sessions
queue_lock = threading.Lock()

# Intelligent Query Cache Management
keyword_cache = {}
verification_cache = {}
cache_lock = threading.Lock()


class UploadStatus(Enum):
    """Status of an upload/process."""
    RECEIVED = "received"
    PROCESSING = "processing"
    QUEUED = "queued"
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class SessionMetadata:
    """Metadata for a processing session."""
    session_id: str
    filename: str
    upload_type: str  # 'pdf' or 'youtube'
    status: UploadStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_chunks: int = 0
    completed_chunks: int = 0
    queue_position: Optional[int] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StatusNotification:
    """Represents a status notification to be sent via SSE."""
    endpoint_type: str  # 'pdf' or 'youtube'
    status: str
    filename: str
    session_id: str
    message: Optional[str] = None
    queue_position: Optional[int] = None
    progress: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)


class StateManager:
    """
    Centralized state management for the process queue system.

    Manages:
    - Session metadata and tracking
    - Status notifications
    - Upload progress
    - Queue position tracking
    - Cleanup of old sessions
    """

    def __init__(self, cleanup_interval_hours: int = 24):
        self.sessions: Dict[str, SessionMetadata] = {}
        self.pending_notifications: List[StatusNotification] = []
        self.lock = threading.Lock()
        self.cleanup_interval_hours = cleanup_interval_hours

        # Start cleanup thread
        self._start_cleanup_thread()

        logger.info("StateManager initialized")

    def create_session(self, session_id: str, filename: str, upload_type: str,
                      metadata: Optional[Dict] = None) -> SessionMetadata:
        """Create a new session with metadata."""
        with self.lock:
            session_metadata = SessionMetadata(
                session_id=session_id,
                filename=filename,
                upload_type=upload_type,
                status=UploadStatus.RECEIVED,
                created_at=datetime.now(),
                metadata=metadata or {}
            )

            self.sessions[session_id] = session_metadata
            logger.info(f"Created session {session_id} for {filename} ({upload_type})")

            return session_metadata

    def update_session_status(self, session_id: str, status: UploadStatus,
                            message: Optional[str] = None, **kwargs) -> bool:
        """Update the status of a session."""
        with self.lock:
            if session_id not in self.sessions:
                logger.warning(f"Attempted to update unknown session: {session_id}")
                return False

            session = self.sessions[session_id]
            old_status = session.status
            session.status = status

            # Update timestamps based on status
            if status == UploadStatus.ACTIVE and session.started_at is None:
                session.started_at = datetime.now()
            elif status in [UploadStatus.COMPLETED, UploadStatus.FAILED]:
                session.completed_at = datetime.now()

            # Update other fields
            for key, value in kwargs.items():
                if hasattr(session, key):
                    setattr(session, key, value)

            logger.info(f"Session {session_id} status: {old_status.value} -> {status.value}")

            # Create status notification
            self._create_status_notification(session, message)

            return True

    def update_chunk_progress(self, session_id: str, completed_chunks: int,
                            total_chunks: Optional[int] = None) -> bool:
        """Update chunk progress for a session."""
        with self.lock:
            if session_id not in self.sessions:
                logger.warning(f"Attempted to update chunk progress for unknown session: {session_id}")
                return False

            session = self.sessions[session_id]
            session.completed_chunks = completed_chunks

            if total_chunks is not None:
                session.total_chunks = total_chunks

            # Calculate progress
            progress = None
            if session.total_chunks > 0:
                progress = (completed_chunks / session.total_chunks) * 100

            logger.debug(f"Session {session_id} chunk progress: {completed_chunks}/{session.total_chunks}")

            # Create progress notification
            notification = StatusNotification(
                endpoint_type=session.upload_type,
                status="processing",
                filename=session.filename,
                session_id=session_id,
                message=f"Processing chunk {completed_chunks}/{session.total_chunks}",
                progress=progress
            )

            self.pending_notifications.append(notification)

            return True

    def update_queue_position(self, session_id: str, position: int) -> bool:
        """Update the queue position for a session."""
        with self.lock:
            if session_id not in self.sessions:
                return False

            session = self.sessions[session_id]
            session.queue_position = position
            session.status = UploadStatus.QUEUED

            # Create queue notification
            notification = StatusNotification(
                endpoint_type=session.upload_type,
                status="queued",
                filename=session.filename,
                session_id=session_id,
                message=f"Queued at position {position}",
                queue_position=position
            )

            self.pending_notifications.append(notification)

            return True

    def _create_status_notification(self, session: SessionMetadata, message: Optional[str] = None):
        """Create a status notification for a session."""
        # Map status to notification message
        status_messages = {
            UploadStatus.RECEIVED: "Upload received",
            UploadStatus.PROCESSING: "Processing started",
            UploadStatus.QUEUED: f"Queued at position {session.queue_position}",
            UploadStatus.ACTIVE: "Processing started",
            UploadStatus.COMPLETED: "Processing completed",
            UploadStatus.FAILED: f"Processing failed: {session.error_message or 'Unknown error'}"
        }

        notification_message = message or status_messages.get(session.status, "Status updated")

        notification = StatusNotification(
            endpoint_type=session.upload_type,
            status=session.status.value,
            filename=session.filename,
            session_id=session.session_id,
            message=notification_message,
            queue_position=session.queue_position
        )

        self.pending_notifications.append(notification)

    def get_session(self, session_id: str) -> Optional[SessionMetadata]:
        """Get session metadata by ID."""
        with self.lock:
            return self.sessions.get(session_id)

    def get_all_sessions(self) -> List[SessionMetadata]:
        """Get all sessions."""
        with self.lock:
            return list(self.sessions.values())

    def get_active_sessions(self) -> List[SessionMetadata]:
        """Get all active sessions."""
        with self.lock:
            return [s for s in self.sessions.values() if s.status == UploadStatus.ACTIVE]

    def get_queued_sessions(self) -> List[SessionMetadata]:
        """Get all queued sessions."""
        with self.lock:
            return [s for s in self.sessions.values() if s.status == UploadStatus.QUEUED]

    def get_pending_notifications(self) -> List[StatusNotification]:
        """Get and clear pending notifications."""
        with self.lock:
            notifications = self.pending_notifications.copy()
            self.pending_notifications.clear()
            return notifications

    def get_session_summary(self) -> Dict[str, Any]:
        """Get a summary of all sessions."""
        with self.lock:
            status_counts = {}
            for status in UploadStatus:
                status_counts[status.value] = 0

            for session in self.sessions.values():
                status_counts[session.status.value] += 1

            return {
                "total_sessions": len(self.sessions),
                "status_counts": status_counts,
                "active_sessions": len([s for s in self.sessions.values() if s.status == UploadStatus.ACTIVE]),
                "queued_sessions": len([s for s in self.sessions.values() if s.status == UploadStatus.QUEUED]),
                "completed_sessions": len([s for s in self.sessions.values() if s.status == UploadStatus.COMPLETED])
            }

    def cleanup_old_sessions(self, max_age_hours: int = None) -> int:
        """Clean up old completed/failed sessions."""
        if max_age_hours is None:
            max_age_hours = self.cleanup_interval_hours

        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        with self.lock:
            sessions_to_remove = []

            for session_id, session in self.sessions.items():
                # Only clean up completed or failed sessions
                if session.status in [UploadStatus.COMPLETED, UploadStatus.FAILED]:
                    # Check if session is old enough
                    check_time = session.completed_at or session.created_at
                    if check_time < cutoff_time:
                        sessions_to_remove.append(session_id)

            # Remove old sessions
            for session_id in sessions_to_remove:
                del self.sessions[session_id]

            if sessions_to_remove:
                logger.info(f"Cleaned up {len(sessions_to_remove)} old sessions")

            return len(sessions_to_remove)

    def _start_cleanup_thread(self):
        """Start the background cleanup thread."""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(3600)  # Run every hour
                    self.cleanup_old_sessions()
                except Exception as e:
                    logger.error(f"Error in cleanup thread: {e}")

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("Started cleanup thread")


# Global state manager instance
state_manager = StateManager()
