"""
Test script for web search functionality.
Run this to test the web search API endpoints.
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5555"
WEB_SEARCH_URL = f"{BASE_URL}/web-search"
HEALTH_CHECK_URL = f"{BASE_URL}/web-search/health"


def test_health_check():
    """Test the web search health check endpoint."""
    print("🔍 Testing web search health check...")
    
    try:
        response = requests.get(HEALTH_CHECK_URL, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Health check passed - SearXNG is connected")
            return True
        else:
            print("⚠️ Health check shows issues with SearXNG")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the server. Make sure the server is running on port 5555")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False


def test_web_search(query="artificial intelligence", num_results=2):
    """Test the web search endpoint."""
    print(f"\n🔍 Testing web search with query: '{query}'")
    
    payload = {
        "query": query,
        "num_results": num_results
    }
    
    try:
        print("Sending request...")
        start_time = time.time()
        
        response = requests.post(
            WEB_SEARCH_URL,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=60  # Web search can take a while
        )
        
        end_time = time.time()
        print(f"Request completed in {end_time - start_time:.2f} seconds")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Web search successful!")
            print(f"Query: {result.get('query')}")
            print(f"Total results: {result.get('total_results')}")
            print(f"Successful fetches: {result.get('successful_fetches')}")
            print(f"Processing time: {result.get('processing_time')} seconds")
            
            # Show results summary
            for i, item in enumerate(result.get('results', []), 1):
                print(f"\n--- Result {i} ---")
                print(f"Search Title: {item.get('search_title', 'N/A')[:80]}...")
                print(f"Fetched Title: {item.get('fetched_title', 'N/A')[:80]}...")
                print(f"URL: {item.get('url')}")
                print(f"Fetch Status: {item.get('fetch_status')}")
                print(f"Content Length: {item.get('content_length', 0)} characters")
                
                if item.get('fetch_status') == 'failed':
                    print(f"Fetch Error: {item.get('fetch_error')}")
                elif item.get('content'):
                    # Show first 200 characters of content
                    content_preview = item.get('content', '')[:200]
                    print(f"Content Preview: {content_preview}...")
            
            return True
        else:
            print(f"❌ Web search failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('message', 'Unknown error')}")
                print(f"Error Type: {error_data.get('error_type', 'Unknown')}")
            except:
                print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out. This might happen if SearXNG is slow or if fetching content takes too long.")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the server. Make sure the server is running on port 5555")
        return False
    except Exception as e:
        print(f"❌ Web search test failed: {str(e)}")
        return False


def test_error_cases():
    """Test error handling."""
    print("\n🔍 Testing error cases...")
    
    # Test empty query
    print("\n--- Testing empty query ---")
    response = requests.post(
        WEB_SEARCH_URL,
        json={"query": ""},
        headers={'Content-Type': 'application/json'},
        timeout=10
    )
    print(f"Empty query - Status: {response.status_code}")
    if response.status_code == 400:
        print("✅ Correctly rejected empty query")
    else:
        print("❌ Should have rejected empty query")
    
    # Test invalid num_results
    print("\n--- Testing invalid num_results ---")
    response = requests.post(
        WEB_SEARCH_URL,
        json={"query": "test", "num_results": 15},
        headers={'Content-Type': 'application/json'},
        timeout=10
    )
    print(f"Invalid num_results - Status: {response.status_code}")
    if response.status_code == 400:
        print("✅ Correctly rejected invalid num_results")
    else:
        print("❌ Should have rejected invalid num_results")
    
    # Test missing query
    print("\n--- Testing missing query ---")
    response = requests.post(
        WEB_SEARCH_URL,
        json={"num_results": 3},
        headers={'Content-Type': 'application/json'},
        timeout=10
    )
    print(f"Missing query - Status: {response.status_code}")
    if response.status_code == 400:
        print("✅ Correctly rejected missing query")
    else:
        print("❌ Should have rejected missing query")


def main():
    """Run all tests."""
    print("🚀 Starting Web Search API Tests")
    print("=" * 50)
    
    # Test health check first
    health_ok = test_health_check()
    
    if health_ok:
        # Test basic web search
        search_ok = test_web_search("Python programming", 2)
        
        if search_ok:
            # Test error cases
            test_error_cases()
            
            print("\n" + "=" * 50)
            print("✅ All tests completed! Web search functionality is working.")
            print("\nYou can now use the web search API in your n8n workflows:")
            print(f"POST {WEB_SEARCH_URL}")
            print('{"query": "your search query", "num_results": 3}')
        else:
            print("\n❌ Basic web search test failed. Check the logs for details.")
    else:
        print("\n❌ Health check failed. Make sure:")
        print("1. Your server is running on port 5555")
        print("2. SearXNG Docker container is running on port 8888")
        print("3. Run: docker ps | grep searxng")


if __name__ == "__main__":
    main()
