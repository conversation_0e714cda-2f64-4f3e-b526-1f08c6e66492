import logging
import os
import queue
import random
import re
import tempfile
import threading
import time
import uuid
import hashlib
import concurrent.futures
from datetime import datetime, timedelta

import chromadb
import requests
from chromadb.config import Settings
from flask import Flask, request, jsonify
from flask_cors import CORS
from unstructured.partition.pdf import partition_pdf
from werkzeug.utils import secure_filename

# Application setup
app = Flask(__name__, static_folder='.', static_url_path='')
CORS(app)

# Logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

# Configuration constants
ALLOWED_EXTENSIONS = {'pdf'}
COLLECTION_NAME = "my_pdf_collection"
N8N_WEBHOOK_URL = "http://localhost:5678/webhook/726e92eb-d600-484b-8f90-3c1f8ca38402"
N8N_ONFAIL_WEBHOOK_URL = "http://localhost:5678/webhook/78e2736e-cfcf-4c1c-bc5d-b004da4e13b7"
CHUNK_PROCESSING_TIMEOUT = 300
MAX_TOKENS_PER_CHUNK = 2000
CHARS_PER_TOKEN = 4

# GPU rest delay between chunks (in seconds)
GPU_REST_DELAY = 5  # Default 5 seconds delay between chunks

# Enhanced Query Performance Configuration (Legacy - kept for backward compatibility)
QUERY_ENHANCEMENT_CONFIG = {
    "adaptive_results": True,
    "keyword_matching_weight": 0.3,
    "semantic_weight": 0.7,
    "diversity_penalty_max": 0.3,
    "min_result_count": 8,
    "max_result_count": 25,
    "default_result_count": 15
}

# Intelligent Query Configuration
INTELLIGENT_QUERY_CONFIG = {
    "ollama_url": "http://localhost:11434",
    "ollama_model": "phi4-mini-reasoning:3.8b",  
    "ollama_timeout": 45,
    "batch_size": 30,
    "max_results": 10,
    "confidence_threshold": 0.65,
    "keyword_extraction_prompt": "Extract 2-4 key phrases from this query: '{user_message}'\n\nReturn ONLY the phrases separated by commas. Do not include any explanations, thinking, or other text.\n\nExample: company name, core services, technical solutions",
    "chunk_verification_prompt": "Rate 0-1 how relevant this text is to answering: '{user_message}'\n\nText: '{chunk_content}'\n\nReturn ONLY a single number with two decimal places (e.g., 0.75). Do not include any other text or explanations."
}
# "qwen3:32b" or "phi4-mini-reasoning:3.8b"

# Application configuration
app.config['MAX_CONTENT_LENGTH'] = None

# Global state management
chunk_queues = {}
chunk_sessions = {}
processing_status = {}
plan_b_sessions = {}  # Track Plan B split processing sessions
queue_lock = threading.Lock()

# Intelligent Query Cache Management
keyword_cache = {}
verification_cache = {}
cache_lock = threading.Lock()

# Custom Exceptions
class OllamaServiceError(Exception):
    """Custom exception for Ollama service issues."""
    pass

# ChromaDB setup
client = chromadb.PersistentClient(
    path="./chroma_data",
    settings=Settings(
        chroma_client_auth_provider="chromadb.auth.token.TokenAuthClientProvider",
        chroma_client_auth_credentials="your-token-here"
    )
)

collection = client.get_or_create_collection(name=COLLECTION_NAME)

# Error handling utilities
def create_error_response(message, status_code=500, error_type="error"):
    """Create standardized error response."""
    return jsonify({
        "status": "error",
        "error_type": error_type,
        "message": message
    }), status_code

def create_success_response(data=None, message="Success"):
    """Create standardized success response."""
    response = {"status": "success", "message": message}
    if data:
        response.update(data)
    return jsonify(response)

def log_and_return_error(logger_msg, user_msg, status_code=500):
    """Log error and return standardized error response."""
    app.logger.error(logger_msg)
    return create_error_response(user_msg, status_code)

# Utility functions
def generate_unique_ids(n):
    """Generate n unique IDs with timestamp and random components."""
    ts = int(time.time())
    return [f"{ts}-{random.randint(1000,9999)}-{i}" for i in range(n)]

def generate_session_id():
    """Generate a unique session ID for chunk processing."""
    return str(uuid.uuid4())

def estimate_token_count(text):
    """Estimate token count using 4 characters per token approximation."""
    return len(text) // CHARS_PER_TOKEN if text else 0

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def find_best_split_point(text, max_position):
    """Find optimal text split position prioritizing natural boundaries."""
    if max_position >= len(text):
        return len(text)

    search_start = max(0, int(max_position * 0.8))

    # Priority order: paragraphs, lines, sentences, words
    patterns = [
        (r'\n\n+', 'paragraph'),
        (r'\n', 'line'),
        (r'[.!?]\s+', 'sentence'),
        (r'\s+', 'word')
    ]

    for pattern, _ in patterns:
        matches = list(re.finditer(pattern, text[search_start:max_position]))
        if matches:
            return search_start + matches[-1].end()

    return max_position

def split_text_for_plan_b(text):
    """Split text into 3 pieces at 33% and 67% points, ensuring splits at sentence/paragraph boundaries."""
    if not text:
        return [], [], []

    # Find split positions at 33% and 67%
    first_split_position = int(len(text) * 0.33)
    second_split_position = int(len(text) * 0.67)

    # Priority order: paragraphs, sentences, lines, words
    patterns = [
        (r'\n\n+', 'paragraph'),
        (r'[.!?]\s+', 'sentence'),
        (r'\n', 'line'),
        (r'\s+', 'word')
    ]

    def find_best_boundary(target_position, search_range=0.1):
        """Find the best boundary near the target position."""
        search_start = max(0, int(target_position * (1 - search_range)))
        search_end = min(len(text), int(target_position * (1 + search_range)))

        for pattern, boundary_type in patterns:
            matches = list(re.finditer(pattern, text[search_start:search_end]))
            if matches:
                best_match = search_start + matches[-1].end()
                app.logger.info(f"Plan B split found at {boundary_type} boundary (position {best_match}/{len(text)})")
                return best_match

        return target_position

    # Find optimal split points
    first_split = find_best_boundary(first_split_position)
    second_split = find_best_boundary(second_split_position)

    # Ensure splits are in order and not too close
    if second_split <= first_split + 50:  # Minimum 50 chars between splits
        second_split = first_split + 50
        if second_split >= len(text):
            # If second split goes beyond text, fall back to 2-piece split
            app.logger.warning("Second split too close to end, falling back to 2-piece split")
            return split_text_into_two_pieces(text)

    # Split the text into 3 pieces
    first_piece = text[:first_split].strip()
    second_piece = text[first_split:second_split].strip()
    third_piece = text[second_split:].strip()

    app.logger.info(f"Plan B 3-piece split: First {len(first_piece)} chars, Second {len(second_piece)} chars, Third {len(third_piece)} chars")

    return first_piece, second_piece, third_piece

def split_text_into_two_pieces(text):
    """Fallback function to split text into 2 pieces if 3-piece split fails."""
    split_position = int(len(text) * 0.5)

    # Find the best split point near 50% position
    search_start = max(0, int(split_position * 0.8))

    patterns = [
        (r'\n\n+', 'paragraph'),
        (r'[.!?]\s+', 'sentence'),
        (r'\n', 'line'),
        (r'\s+', 'word')
    ]

    best_split = split_position
    for pattern, boundary_type in patterns:
        matches = list(re.finditer(pattern, text[search_start:split_position]))
        if matches:
            best_split = search_start + matches[-1].end()
            app.logger.info(f"Fallback 2-piece split at {boundary_type} boundary")
            break

    first_piece = text[:best_split].strip()
    second_piece = text[best_split:].strip()

    app.logger.info(f"Fallback 2-piece split: First {len(first_piece)} chars, Second {len(second_piece)} chars")

    return first_piece, second_piece, ""  # Return empty third piece for consistency

def pre_chunk_text(text, max_tokens=MAX_TOKENS_PER_CHUNK):
    """Split text into chunks respecting token limits and natural boundaries."""
    if not text:
        return []

    total_tokens = estimate_token_count(text)
    app.logger.info(f"Text: {len(text)} chars, ~{total_tokens} tokens")

    if total_tokens <= max_tokens:
        app.logger.info("Text within token limit, no chunking needed")
        return [text]

    app.logger.info(f"Chunking text (limit: {max_tokens} tokens)")

    chunks = []
    current_position = 0
    chunk_number = 1

    while current_position < len(text):
        max_chars = max_tokens * CHARS_PER_TOKEN
        end_position = min(current_position + max_chars, len(text))
        split_position = find_best_split_point(text, end_position)

        chunk = text[current_position:split_position].strip()

        if chunk:
            chunk_tokens = estimate_token_count(chunk)
            app.logger.info(f"Chunk {chunk_number}: {len(chunk)} chars, ~{chunk_tokens} tokens")
            chunks.append(chunk)
            chunk_number += 1

        if split_position > current_position:
            current_position = split_position
        else:
            app.logger.warning("Split position didn't advance, forcing move")
            current_position = min(current_position + max_chars // 2, len(text))

    app.logger.info(f"Created {len(chunks)} chunks")
    return chunks

def looks_like_table(text):
    """Detect if text content appears to be a table using heuristics."""
    lines = text.split('\n')
    if len(lines) < 3:
        return False

    table_keywords = ['Technical Specification', 'Interface', 'System', 'Network', 'Storage', 'Display']
    has_keywords = any(keyword in text for keyword in table_keywords)
    has_separators = '|' in text or '\t' in text
    has_multiple_spaces = any('  ' in line for line in lines)
    has_colons = text.count(':') > 3

    return has_keywords or has_separators or (has_multiple_spaces and has_colons)

def process_table_element(element):
    """Process table elements to preserve structure and improve readability."""
    try:
        element_text = str(element)
        lines = element_text.split('\n')

        table_indicators = ['|', '\t', '  ', 'Technical Specification', 'Interface', 'System']
        has_structure = any(indicator in element_text for indicator in table_indicators)

        if has_structure and len(lines) > 2:
            formatted_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                if '|' in line:
                    parts = [part.strip() for part in line.split('|') if part.strip()]
                    if len(parts) >= 2:
                        formatted_lines.append(f"{parts[0]}: {' | '.join(parts[1:])}")
                    else:
                        formatted_lines.append(line)
                elif '\t' in line or '  ' in line:
                    parts = re.split(r'\s{2,}|\t+', line)
                    parts = [part.strip() for part in parts if part.strip()]
                    if len(parts) >= 2:
                        formatted_lines.append(f"{parts[0]}: {' | '.join(parts[1:])}")
                    else:
                        formatted_lines.append(line)
                else:
                    formatted_lines.append(line)

            return "[TABLE]\n" + "\n".join(formatted_lines) + "\n[/TABLE]"
        else:
            return f"[TABLE]\n{element_text}\n[/TABLE]"

    except Exception:
        return f"[TABLE]\n{str(element)}\n[/TABLE]"

def trigger_n8n_workflow(extracted_text, filename=None):
    """Trigger n8n workflow with extracted text, handling chunking if needed."""
    try:
        text_chunks = pre_chunk_text(extracted_text)
        session_id = generate_session_id()

        app.logger.info(f"Session {session_id}: {len(text_chunks)} chunks for {filename}")

        if len(text_chunks) == 1:
            return _process_single_chunk(text_chunks[0], filename, session_id)
        else:
            app.logger.info(f"Multiple chunks detected, using {GPU_REST_DELAY}s delay between chunks")
            return _process_multiple_chunks(text_chunks, filename, session_id)

    except Exception as e:
        app.logger.error(f"Error in trigger_n8n_workflow: {str(e)}")
        return False

def _process_single_chunk(chunk, filename, session_id):
    """Process a single text chunk."""
    with queue_lock:
        processing_status[session_id] = {
            'status': 'processing',
            'total_chunks': 1,
            'current_chunk': filename,
            'last_completed': None,
            'last_status': None,
            'last_update': datetime.now(),
            'filename': filename,
            'error': None
        }

    success = _send_chunk_to_n8n(chunk, filename, session_id, 1, original_filename=filename)

    if success:
        with queue_lock:
            processing_status[session_id]['current_chunk'] = filename
        return True
    else:
        with queue_lock:
            if session_id in processing_status:
                del processing_status[session_id]
        return False

def _process_multiple_chunks(chunks, filename, session_id):
    """Process multiple text chunks with queue management."""
    try:
        with queue_lock:
            # Initialize chunk queue for this session
            chunk_queues[session_id] = queue.Queue()

            # Add all chunks except the first one to the queue
            for i, chunk in enumerate(chunks[1:], 2):
                chunk_queues[session_id].put((chunk, f"{filename}_chunk_{i}"))

            # Initialize processing status
            chunk_number = 1
            processing_status[session_id] = {
                'status': 'processing',
                'total_chunks': len(chunks),
                'current_chunk': f"{filename}_chunk_{chunk_number}",
                'processed_chunks': 0,
                'last_completed': None,
                'last_status': None,
                'last_update': datetime.now(),
                'filename': filename,
                'error': None
            }

        app.logger.info(f"Session {session_id}: Processing {len(chunks)} chunks with {GPU_REST_DELAY}s delay between chunks")

        # Send the first chunk immediately
        success = _send_chunk_to_n8n(chunks[0], f"{filename}_chunk_1", session_id, 1, original_filename=filename)

        if success:
            with queue_lock:
                processing_status[session_id]['current_chunk'] = f"{filename}_chunk_1"
            return True
        else:
            # Clean up on failure
            with queue_lock:
                if session_id in processing_status:
                    del processing_status[session_id]
                if session_id in chunk_queues:
                    del chunk_queues[session_id]
            return False

    except Exception as e:
        app.logger.error(f"Error in _process_multiple_chunks: {str(e)}")
        return False

def _validate_pdf_request():
    """Validate PDF upload request."""
    if 'pdfFile' not in request.files:
        return log_and_return_error(
            "No file uploaded in request",
            "No file uploaded",
            400
        )

    file = request.files['pdfFile']

    if file.filename == '':
        return log_and_return_error(
            "No file selected",
            "No file selected",
            400
        )

    if not allowed_file(file.filename):
        return log_and_return_error(
            f"Invalid file type: {file.filename}",
            "Only PDF files are allowed",
            400
        )

    return None

def _extract_pdf_content(temp_file_path):
    """Extract content from PDF using unstructured library."""
    app.logger.info("Starting PDF content extraction")

    try:
        app.logger.info("Trying hi_res strategy...")
        elements = partition_pdf(
            temp_file_path,
            strategy="hi_res",
            infer_table_structure=True,
            extract_images_in_pdf=True,
            extract_image_block_types=["Image", "Table"]
        )
    except Exception as e:
        app.logger.warning(f"Hi-res strategy failed: {str(e)}, trying fast strategy...")
        elements = partition_pdf(
            temp_file_path,
            strategy="fast",
            infer_table_structure=True,
            extract_images_in_pdf=False
        )

    processed_content = []
    table_count = 0

    for element in elements:
        element_type = str(type(element).__name__)
        element_text = str(element)

        if element_type == "Table":
            app.logger.info(f"Found table element ({len(element_text)} chars)")
            processed_content.append(process_table_element(element))
            table_count += 1
        elif element_type == "Image":
            processed_content.append(f"[IMAGE] {element_text}")
        else:
            if looks_like_table(element_text):
                app.logger.info(f"Detected potential table in {element_type}")
                processed_content.append(process_table_element(element))
                table_count += 1
            else:
                processed_content.append(element_text)

    extracted_text = "\n".join(processed_content)
    app.logger.info(f"Extracted {len(elements)} elements ({table_count} tables)")

    return extracted_text

def _build_pdf_response(extracted_text, chunk_count, total_tokens, workflow_triggered):
    """Build response data for PDF processing."""
    return {
        "status": "success",
        "message": "PDF processed successfully",
        "text": extracted_text,
        "workflow_triggered": workflow_triggered,
        "text_stats": {
            "total_characters": len(extracted_text),
            "estimated_tokens": total_tokens,
            "chunks_created": chunk_count,
            "chunking_applied": chunk_count > 1
        }
    }

def _send_chunk_to_n8n(text_chunk, filename=None, session_id=None, chunk_number=None, original_filename=None):
    """Send a single text chunk to n8n webhook."""
    try:
        # Use original filename for source to maintain consistent source naming in ChromaDB
        # The filename parameter may contain chunk identifiers for internal tracking
        source_name = original_filename if original_filename else filename

        # Extract original filename if filename contains chunk identifier
        if filename and "_chunk_" in filename and not original_filename:
            source_name = filename.split("_chunk_")[0]

        payload = {
            "text": text_chunk,
            "source": source_name,  # Always use original filename for ChromaDB storage
            "session_id": session_id,
            "chunk_number": chunk_number,
            "chunk_identifier": filename,  # Keep chunk identifier for tracking
            "callback_url": "http://localhost:5555/chunk_complete"
        }

        response = requests.post(
            N8N_WEBHOOK_URL,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        if response.status_code == 200:
            app.logger.info(f"Sent chunk to n8n (source: {filename})")
            return True
        else:
            app.logger.error(f"n8n webhook error {response.status_code}: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        app.logger.error(f"Failed to send chunk to n8n: {str(e)}")
        return False
    except Exception as e:
        app.logger.error(f"Error sending chunk to n8n: {str(e)}")
        return False

def _send_chunk_to_onfail_webhook(text_chunk, session_data, piece_number):
    """Send a text chunk to the OnFail workflow webhook."""
    try:
        payload = {
            "text": text_chunk,
            "source": session_data.get('source'),
            "session_id": session_data.get('session_id'),
            "chunk_number": session_data.get('chunk_number'),
            "plan_b_piece": piece_number,
            "original_session_id": session_data.get('session_id')
        }

        response = requests.post(
            N8N_ONFAIL_WEBHOOK_URL,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        if response.status_code == 200:
            app.logger.info(f"Sent Plan B piece {piece_number} to OnFail webhook (session: {session_data.get('session_id')})")
            return True
        else:
            app.logger.error(f"OnFail webhook error {response.status_code}: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        app.logger.error(f"Failed to send chunk to OnFail webhook: {str(e)}")
        return False
    except Exception as e:
        app.logger.error(f"Error sending chunk to OnFail webhook: {str(e)}")
        return False

@app.route("/upload-pdf", methods=["POST"])
def upload_pdf():
    """Handle PDF upload, validation, and content extraction."""
    try:
        # Validate request
        validation_error = _validate_pdf_request()
        if validation_error:
            return validation_error

        file = request.files['pdfFile']
        app.logger.info(f"Processing PDF: {file.filename}")

        # Process file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_file_path = temp_file.name

        try:
            extracted_text = _extract_pdf_content(temp_file_path)
            text_chunks = pre_chunk_text(extracted_text)
            total_tokens = estimate_token_count(extracted_text)

            workflow_triggered = trigger_n8n_workflow(extracted_text, file.filename)

            response_data = _build_pdf_response(
                extracted_text, len(text_chunks), total_tokens, workflow_triggered
            )

            if len(text_chunks) > 1:
                response_data["message"] = f"PDF split into {len(text_chunks)} chunks for processing"
                app.logger.info(f"PDF split into {len(text_chunks)} chunks")

            if not workflow_triggered:
                response_data["warning"] = "PDF processed but n8n workflow failed. Check if n8n is running."

            return jsonify(response_data), 200

        except Exception as e:
            app.logger.error(f"Failed to extract PDF content: {str(e)}")
            return jsonify({
                "status": "error",
                "message": f"Failed to process PDF: {str(e)}"
            }), 500

        finally:
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                app.logger.warning(f"Failed to delete temp file: {str(e)}")

    except Exception as e:
        app.logger.error(f"Error in upload_pdf: {str(e)}")
        return jsonify({
            "status": "error",
            "message": "Internal server error occurred"
        }), 500

@app.route("/plan_b", methods=["POST"])
def plan_b_processing():
    """Handle Plan B processing when AI Agent fails - split text and send to OnFail workflow."""
    try:
        data = request.get_json()
        if not data:
            return create_error_response("No JSON data provided", 400, "validation_error")

        # Extract session data
        text = data.get('text')
        session_id = data.get('session_id')
        chunk_number = data.get('chunk_number')
        source = data.get('source')

        if not text:
            return create_error_response("Text is required", 400, "validation_error")

        app.logger.info(f"Plan B processing initiated for session {session_id}, chunk {chunk_number}")

        # Split text into 3 pieces
        first_piece, second_piece, third_piece = split_text_for_plan_b(text)

        # Check if we have valid pieces
        pieces = [p for p in [first_piece, second_piece, third_piece] if p.strip()]

        if len(pieces) < 2:
            app.logger.warning("Plan B split resulted in insufficient pieces, sending original text to OnFail")
            # If split fails, send original text as single piece
            session_data = {
                'session_id': session_id,
                'chunk_number': chunk_number,
                'source': source
            }
            success = _send_chunk_to_onfail_webhook(text, session_data, 1)
            if success:
                return create_success_response({
                    "pieces_sent": 1,
                    "split_successful": False
                }, "Plan B processing completed (no split)")
            else:
                return create_error_response("Failed to send text to OnFail workflow", 500)

        # Create Plan B session tracking
        plan_b_session_id = f"planb_{session_id}_{chunk_number}"
        total_pieces = len(pieces)

        with queue_lock:
            plan_b_sessions[plan_b_session_id] = {
                'original_session_id': session_id,
                'original_chunk_number': chunk_number,
                'source': source,
                'total_pieces': total_pieces,
                'pieces_sent': 0,
                'pieces_completed': 0,
                'pieces': pieces,  # Store all pieces
                'first_piece': first_piece,
                'second_piece': second_piece,
                'third_piece': third_piece if third_piece else None,
                'status': 'processing',
                'created_at': datetime.now()
            }

        # Send first piece immediately
        session_data = {
            'session_id': plan_b_session_id,
            'chunk_number': f"{chunk_number}_piece_1",
            'source': source
        }

        success_first = _send_chunk_to_onfail_webhook(first_piece, session_data, 1)

        if success_first:
            with queue_lock:
                plan_b_sessions[plan_b_session_id]['pieces_sent'] = 1

            app.logger.info(f"Plan B first piece sent successfully for session {plan_b_session_id}")

            return create_success_response({
                "plan_b_session_id": plan_b_session_id,
                "pieces_sent": 1,
                "total_pieces": total_pieces,
                "split_successful": True,
                "first_piece_length": len(first_piece),
                "second_piece_length": len(second_piece),
                "third_piece_length": len(third_piece) if third_piece else 0
            }, f"Plan B processing initiated - first piece sent ({total_pieces} pieces total)")
        else:
            # Clean up on failure
            with queue_lock:
                if plan_b_session_id in plan_b_sessions:
                    del plan_b_sessions[plan_b_session_id]

            return create_error_response("Failed to send first piece to OnFail workflow", 500)

    except Exception as e:
        app.logger.error(f"Error in plan_b_processing: {str(e)}")
        return create_error_response("Internal server error in Plan B processing", 500)

# ChromaDB Operations
def _parse_json_field(field_value, field_name):
    """
    Parse a field that might be a JSON string (common with n8n).

    Args:
        field_value: The field value to parse
        field_name: Name of the field for logging

    Returns:
        Parsed value or original value if not a JSON string
    """
    if isinstance(field_value, str):
        try:
            import json
            parsed_value = json.loads(field_value)
            app.logger.info(f"Parsed {field_name} from JSON string: {type(parsed_value)}")
            return parsed_value
        except json.JSONDecodeError:
            # If it's not valid JSON, return as-is (might be a simple string)
            app.logger.info(f"{field_name} is a string but not valid JSON, using as-is")
            return field_value
    return field_value

@app.route("/add", methods=["POST"])
def add_to_chroma():
    """Add documents to ChromaDB collection with enhanced metadata support."""
    try:
        data = request.get_json(force=True)
        if not data:
            return create_error_response("No JSON data provided", 400, "validation_error")

        app.logger.info(f"Add request received with keys: {list(data.keys())}")
        app.logger.info(f"Raw request data keys and types: {[(k, type(v)) for k, v in data.items()]}")

        # Parse input fields that might be JSON strings (common with n8n)
        ids = _parse_json_field(data.get("ids"), "ids")
        documents = _parse_json_field(data.get("documents"), "documents")
        embeddings = data.get("embedding")  # embeddings are usually already parsed correctly

        # Handle both 'metadatas' and 'metedatas' (typo in n8n)
        raw_metadatas = data.get("metadatas") or data.get("metedatas")
        raw_metadatas = _parse_json_field(raw_metadatas, "metadatas")

        # Convert single metadata object to list if needed
        metadatas = [raw_metadatas] if isinstance(raw_metadatas, dict) else raw_metadatas

        # Normalize ids and documents to lists if they're single strings
        if isinstance(ids, str):
            ids = [ids]
            app.logger.info("Converted single ID string to list")

        if isinstance(documents, str):
            documents = [documents]
            app.logger.info("Converted single document string to list")

        # Enhanced logging for debugging
        app.logger.info(f"IDs type: {type(ids)}, count: {len(ids) if isinstance(ids, list) else 'N/A'}")
        app.logger.info(f"Documents type: {type(documents)}, count: {len(documents) if isinstance(documents, list) else 'N/A'}")
        app.logger.info(f"Embeddings type: {type(embeddings)}, count: {len(embeddings) if isinstance(embeddings, list) else 'N/A'}")
        app.logger.info(f"Metadatas type: {type(metadatas)}, count: {len(metadatas) if isinstance(metadatas, list) else 'N/A'}")

        if isinstance(ids, list) and len(ids) > 0:
            app.logger.info(f"First few IDs: {ids[:3]}")

        if isinstance(documents, list) and len(documents) > 0:
            app.logger.info(f"First document length: {len(documents[0]) if documents[0] else 0} chars")

        if isinstance(embeddings, list) and len(embeddings) > 0:
            app.logger.info(f"First embedding type: {type(embeddings[0])}")
            if isinstance(embeddings[0], list):
                app.logger.info(f"First embedding dimension: {len(embeddings[0])}")

        if isinstance(metadatas, list) and len(metadatas) > 0:
            app.logger.info(f"First metadata: {metadatas[0]}")
        elif metadatas:
            app.logger.info(f"Metadatas (non-list): {metadatas}")

        # Validate required fields
        if not (ids and documents and embeddings):
            return create_error_response(
                "ids, documents, and embeddings are required",
                400,
                "validation_error"
            )

        # Validate and normalize array lengths
        ids_count = len(ids) if isinstance(ids, list) else 1
        docs_count = len(documents) if isinstance(documents, list) else 1

        # Handle embeddings - check if it's a 2D array (list of vectors) or 1D array (single flattened vector)
        if isinstance(embeddings, list):
            if len(embeddings) > 0 and isinstance(embeddings[0], list):
                # 2D array - list of embedding vectors
                embeddings_count = len(embeddings)
                app.logger.info(f"Embeddings: 2D array with {embeddings_count} vectors of dimension {len(embeddings[0])}")
            else:
                # 1D array - single flattened embedding vector, treat as single embedding
                embeddings_count = 1
                embeddings = [embeddings]  # Convert to 2D array
                app.logger.info(f"Embeddings: 1D array with {len(embeddings[0])} dimensions, converted to single vector")
        else:
            embeddings_count = 1
            app.logger.info(f"Embeddings: Non-list type {type(embeddings)}")

        app.logger.info(f"Array lengths - IDs: {ids_count}, Documents: {docs_count}, Embeddings: {embeddings_count}")

        if not (ids_count == docs_count == embeddings_count):
            return create_error_response(
                f"Array length mismatch: ids({ids_count}), documents({docs_count}), embeddings({embeddings_count}) must be equal",
                400,
                "validation_error"
            )

        # Validate and enhance metadata structure
        processed_metadatas = _process_and_validate_metadata(metadatas, ids_count)

        collection.add(
            ids=ids,
            documents=documents,
            embeddings=embeddings,
            metadatas=processed_metadatas
        )

        app.logger.info(f"Successfully added {ids_count} documents to ChromaDB")
        return create_success_response({
            "inserted": ids_count,
            "ids": ids if isinstance(ids, list) else [ids],
            "metadata_fields_processed": _get_metadata_summary(processed_metadatas)
        }, f"Added {ids_count} documents")

    except Exception as e:
        app.logger.error(f"Add operation failed: {e}")
        return create_error_response(f"Failed to add documents: {str(e)}", 500)

def _process_and_validate_metadata(metadatas, expected_count):
    """
    Process and validate metadata to ensure it contains required fields and proper structure.

    Args:
        metadatas: Raw metadata from request (list or single dict)
        expected_count: Expected number of metadata objects

    Returns:
        List of processed metadata dictionaries
    """
    if not metadatas:
        app.logger.warning("No metadata provided, creating empty metadata objects")
        return [{}] * expected_count

    # Ensure metadatas is a list
    if isinstance(metadatas, dict):
        metadatas = [metadatas]

    processed_metadatas = []

    for i, metadata in enumerate(metadatas):
        if not isinstance(metadata, dict):
            app.logger.warning(f"Metadata at index {i} is not a dictionary, converting to empty dict")
            metadata = {}

        # Create a copy to avoid modifying original
        processed_metadata = metadata.copy()

        # Validate and set default values for expected fields
        # Source field (required for filtering functionality)
        if "Source" not in processed_metadata or not processed_metadata["Source"]:
            app.logger.warning(f"Metadata at index {i} missing 'Source' field")
            processed_metadata["Source"] = "unknown"

        # Title field (optional, for enhanced search)
        if "Title" not in processed_metadata:
            processed_metadata["Title"] = ""

        # Remark field (optional, for additional context)
        if "Remark" not in processed_metadata:
            processed_metadata["Remark"] = ""

        # Log the processed metadata structure
        app.logger.info(f"Processed metadata {i}: Source='{processed_metadata['Source']}', "
                       f"Title='{processed_metadata['Title']}', Remark='{processed_metadata['Remark']}'")

        processed_metadatas.append(processed_metadata)

    # If we have fewer metadata objects than expected, duplicate the last one
    while len(processed_metadatas) < expected_count:
        if processed_metadatas:
            processed_metadatas.append(processed_metadatas[-1].copy())
        else:
            processed_metadatas.append({"Source": "unknown", "Title": "", "Remark": ""})

    # If we have more metadata objects than expected, truncate
    if len(processed_metadatas) > expected_count:
        app.logger.warning(f"More metadata objects ({len(processed_metadatas)}) than documents ({expected_count}), truncating")
        processed_metadatas = processed_metadatas[:expected_count]

    return processed_metadatas

def _get_metadata_summary(metadatas):
    """
    Generate a summary of metadata fields for logging and response.

    Args:
        metadatas: List of processed metadata dictionaries

    Returns:
        Dictionary with metadata field statistics
    """
    if not metadatas:
        return {"total_count": 0, "fields": []}

    # Count field usage
    field_counts = {}
    sources = set()

    for metadata in metadatas:
        for field, value in metadata.items():
            if field not in field_counts:
                field_counts[field] = {"present": 0, "non_empty": 0}

            field_counts[field]["present"] += 1
            if value and str(value).strip():
                field_counts[field]["non_empty"] += 1

            if field == "Source" and value:
                sources.add(value)

    return {
        "total_count": len(metadatas),
        "unique_sources": len(sources),
        "field_statistics": field_counts,
        "sources": list(sources)[:5]  # Show first 5 sources
    }

# Intelligent Query System Functions

def _get_cache_key(data):
    """Generate cache key from data."""
    return hashlib.md5(str(data).encode()).hexdigest()

def _cache_keywords(message, keywords):
    """Cache extracted keywords."""
    with cache_lock:
        cache_key = _get_cache_key(message)
        keyword_cache[cache_key] = keywords
        app.logger.info(f"Cached keywords for message hash: {cache_key[:8]}")

def _get_cached_keywords(message):
    """Retrieve cached keywords."""
    with cache_lock:
        cache_key = _get_cache_key(message)
        return keyword_cache.get(cache_key)

def _cache_verification(message, chunk_content, confidence):
    """Cache chunk verification result."""
    with cache_lock:
        cache_key = _get_cache_key(f"{message}|{chunk_content}")
        verification_cache[cache_key] = confidence
        app.logger.info(f"Cached verification for chunk hash: {cache_key[:8]}")

def _get_cached_verification(message, chunk_content):
    """Retrieve cached verification result."""
    with cache_lock:
        cache_key = _get_cache_key(f"{message}|{chunk_content}")
        return verification_cache.get(cache_key)

def _clear_request_cache():
    """Clear all caches after request completion."""
    with cache_lock:
        keyword_cache.clear()
        verification_cache.clear()
        app.logger.info("Cleared request caches")

def _call_ollama(prompt, timeout=None):
    """Call Ollama API with error handling and timeout."""
    if timeout is None:
        timeout = INTELLIGENT_QUERY_CONFIG["ollama_timeout"]

    try:
        app.logger.info(f"Calling Ollama with prompt length: {len(prompt)}")

        response = requests.post(
            f"{INTELLIGENT_QUERY_CONFIG['ollama_url']}/api/generate",
            json={
                "model": INTELLIGENT_QUERY_CONFIG["ollama_model"],
                "prompt": prompt,
                "stream": False
            },
            timeout=timeout
        )

        if response.status_code != 200:
            raise OllamaServiceError(f"Ollama API returned status {response.status_code}: {response.text}")

        result = response.json()
        if "response" not in result:
            raise OllamaServiceError("Invalid response format from Ollama")

        app.logger.info(f"Ollama response received, length: {len(result['response'])}")
        return result["response"].strip()

    except requests.exceptions.Timeout:
        raise OllamaServiceError(f"Ollama request timed out after {timeout} seconds")
    except requests.exceptions.ConnectionError:
        raise OllamaServiceError("Cannot connect to Ollama service")
    except requests.exceptions.RequestException as e:
        raise OllamaServiceError(f"Ollama request failed: {str(e)}")
    except Exception as e:
        raise OllamaServiceError(f"Unexpected error calling Ollama: {str(e)}")

def _extract_keywords_with_ollama(user_message):
    """Extract and correct keywords from user message using Ollama."""
    # Handle empty or None message
    if not user_message or not user_message.strip():
        app.logger.info("Empty user message, returning empty keywords")
        return []

    # Check cache first
    cached_keywords = _get_cached_keywords(user_message)
    if cached_keywords:
        app.logger.info(f"Using cached keywords: {cached_keywords}")
        return cached_keywords

    try:
        prompt = INTELLIGENT_QUERY_CONFIG["keyword_extraction_prompt"].format(user_message=user_message)
        response = _call_ollama(prompt)

        # Parse keywords from response - handle verbose output
        # Look for the last line that contains comma-separated keywords
        lines = response.strip().split('\n')
        keywords = []

        # Try to find the actual keywords (usually the last meaningful line)
        for line in reversed(lines):
            line = line.strip()
            if line and ',' in line and not line.startswith('<think>') and not line.startswith('Okay'):
                # This looks like comma-separated keywords
                keywords = [keyword.strip() for keyword in line.split(',') if keyword.strip()]
                break

        # Fallback: if no comma-separated line found, try to extract from the response
        if not keywords:
            # Look for quoted phrases or key terms
            import re
            # Extract quoted phrases
            quoted_phrases = re.findall(r'"([^"]*)"', response)
            if quoted_phrases:
                keywords = [phrase.strip() for phrase in quoted_phrases if phrase.strip()]
            else:
                # Last resort: split the entire response and filter
                all_parts = [part.strip() for part in response.split(',') if part.strip()]
                # Filter out obvious non-keywords
                keywords = [part for part in all_parts[:10] if len(part) < 50 and not part.startswith('<think>')]

        # Cache the result
        _cache_keywords(user_message, keywords)

        app.logger.info(f"Extracted keywords: {keywords}")
        return keywords

    except OllamaServiceError as e:
        app.logger.error(f"Keyword extraction failed: {e}")
        raise
    except Exception as e:
        app.logger.error(f"Unexpected error in keyword extraction: {e}")
        raise OllamaServiceError(f"Keyword extraction failed: {str(e)}")

def _verify_chunk_relevance(user_message, chunk_content):
    """Verify chunk relevance using Ollama, returns confidence score."""
    # Check cache first
    cached_confidence = _get_cached_verification(user_message, chunk_content)
    if cached_confidence is not None:
        app.logger.info(f"Using cached verification: {cached_confidence}")
        return cached_confidence

    try:
        prompt = INTELLIGENT_QUERY_CONFIG["chunk_verification_prompt"].format(
            user_message=user_message,
            chunk_content=chunk_content[:1000]  # Limit chunk size for efficiency
        )
        response = _call_ollama(prompt)

        # Parse confidence score - handle various response formats
        try:
            # Clean the response first
            cleaned_response = response.strip()

            # Extract the first number from the response (handle "0.75, defaulting to 0.0" format)
            import re
            numbers = re.findall(r'\b0\.\d{2}\b|\b1\.00\b|\b[01]\b', cleaned_response)

            if numbers:
                confidence = float(numbers[0])  # Take the first valid number
                app.logger.info(f"Extracted confidence score: {confidence} from response: '{cleaned_response[:50]}...'")
            else:
                # Try to parse the entire response as float
                confidence = float(cleaned_response)

            # Validate range
            if not (0.0 <= confidence <= 1.0):
                app.logger.warning(f"Invalid confidence score: {confidence}, defaulting to 0.0")
                confidence = 0.0

        except ValueError:
            app.logger.warning(f"Could not parse confidence score from: '{response[:100]}...', defaulting to 0.0")
            confidence = 0.0

        # Cache the result
        _cache_verification(user_message, chunk_content, confidence)

        app.logger.info(f"Chunk verification confidence: {confidence}")
        return confidence

    except OllamaServiceError as e:
        app.logger.error(f"Chunk verification failed: {e}")
        raise
    except Exception as e:
        app.logger.error(f"Unexpected error in chunk verification: {e}")
        raise OllamaServiceError(f"Chunk verification failed: {str(e)}")

def _detect_source_pattern(message):
    """Detect @filename.pdf pattern in message, return (source, cleaned_message)."""
    if not message or not isinstance(message, str):
        return None, message

    message = message.strip()

    # Check if message starts with @filename pattern
    if message.startswith('@'):
        # Find the first space to separate the source from the rest of the query
        space_index = message.find(' ')
        if space_index > 1:  # Ensure there's content after @
            source_part = message[1:space_index]  # Remove @ prefix
            cleaned_message = message[space_index + 1:].strip()  # Remove source and leading spaces

            # Validate that source_part looks like a filename (contains a dot)
            if '.' in source_part:
                app.logger.info(f"Source pattern detected: '{source_part}', cleaned message: '{cleaned_message}'")
                return source_part, cleaned_message
            else:
                app.logger.warning(f"Invalid source format: '{source_part}' (no file extension)")
        else:
            app.logger.warning(f"Malformed source query: '{message}' (no space after source)")

    # No valid source filter found, return original message
    return None, message

def _filter_chunks_by_metadata(keywords, source_filter=None):
    """Filter chunks where Title OR Remark contains keywords using Python filtering."""
    try:
        # Get all documents first (ChromaDB doesn't support text contains operations)
        if source_filter:
            # Filter by source first
            results = collection.get(
                where={"Source": source_filter},
                include=["metadatas", "documents"]
            )
        else:
            # Get all documents
            results = collection.get(include=["metadatas", "documents"])

        app.logger.info(f"Retrieved {len(results.get('ids', []))} total documents for filtering")

        # Convert to list of chunk dictionaries
        all_chunks = []
        if results and results.get("ids"):
            for i, chunk_id in enumerate(results["ids"]):
                all_chunks.append({
                    "id": chunk_id,
                    "document": results["documents"][i] if i < len(results["documents"]) else "",
                    "metadata": results["metadatas"][i] if i < len(results["metadatas"]) else {}
                })

        # If no keywords, return all chunks (filtered by source if specified)
        if not keywords:
            app.logger.info(f"No keywords provided, returning {len(all_chunks)} chunks")
            return all_chunks

        # Filter chunks by keywords in Python
        filtered_chunks = []
        for chunk in all_chunks:
            metadata = chunk["metadata"]
            title = metadata.get("Title", "").lower()
            remark = metadata.get("Remark", "").lower()
            document_content = chunk["document"].lower()



            # Check if any keyword matches in Title, Remark, or Document content
            keyword_found = False
            for keyword in keywords:
                keyword_lower = keyword.lower().strip()
                if keyword_lower and (keyword_lower in title or keyword_lower in remark or keyword_lower in document_content):
                    keyword_found = True
                    app.logger.info(f"Keyword '{keyword}' found in chunk {chunk['id'][:10]}...")
                    break

            if keyword_found:
                filtered_chunks.append(chunk)

        app.logger.info(f"Filtered {len(filtered_chunks)} chunks by metadata (from {len(all_chunks)} total)")
        return filtered_chunks

    except Exception as e:
        app.logger.error(f"Metadata filtering failed: {e}")
        raise

def _verify_chunks_parallel(chunks, user_message):
    """Verify multiple chunks in parallel."""
    try:
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = {
                executor.submit(_verify_chunk_relevance, user_message, chunk["document"]): chunk
                for chunk in chunks
            }

            results = []
            for future in concurrent.futures.as_completed(futures):
                try:
                    confidence = future.result()
                    chunk = futures[future]
                    if confidence >= INTELLIGENT_QUERY_CONFIG["confidence_threshold"]:
                        results.append((chunk, confidence))
                        app.logger.info(f"Chunk {chunk['id'][:8]}... passed verification with confidence {confidence}")
                    else:
                        app.logger.info(f"Chunk {chunk['id'][:8]}... failed verification with confidence {confidence}")
                except Exception as e:
                    chunk = futures[future]
                    app.logger.error(f"Chunk verification failed for {chunk['id'][:8]}...: {e}")
                    raise

            # Sort by confidence score (highest first)
            results.sort(key=lambda x: x[1], reverse=True)
            return results

    except Exception as e:
        app.logger.error(f"Parallel chunk verification failed: {e}")
        raise

def _process_chunks_in_batches(embedding_results, user_message, batch_size=30):
    """Process chunks in batches with parallel verification."""
    try:
        if not embedding_results or not embedding_results.get('ids') or not embedding_results['ids'][0]:
            return _no_results_response()

        ids = embedding_results['ids'][0]
        documents = embedding_results.get('documents', [[]])[0]
        distances = embedding_results.get('distances', [[]])[0]
        metadatas = embedding_results.get('metadatas', [[]])[0]

        # Create chunk objects
        all_chunks = []
        for i in range(len(ids)):
            all_chunks.append({
                'id': ids[i],
                'document': documents[i] if i < len(documents) else "",
                'distance': distances[i] if i < len(distances) else 1.0,
                'metadata': metadatas[i] if i < len(metadatas) else {}
            })

        verified_chunks = []
        max_results = INTELLIGENT_QUERY_CONFIG["max_results"]

        # Process in batches
        for batch_start in range(0, len(all_chunks), batch_size):
            if len(verified_chunks) >= max_results:
                break

            batch_end = min(batch_start + batch_size, len(all_chunks))
            batch_chunks = all_chunks[batch_start:batch_end]

            app.logger.info(f"Processing batch {batch_start//batch_size + 1}: chunks {batch_start+1}-{batch_end}")

            # Verify batch in parallel
            batch_results = _verify_chunks_parallel(batch_chunks, user_message)

            # Add verified chunks to results
            for chunk, confidence in batch_results:
                if len(verified_chunks) < max_results:
                    chunk['confidence'] = confidence
                    verified_chunks.append(chunk)
                else:
                    break

            app.logger.info(f"Batch {batch_start//batch_size + 1} completed: {len(batch_results)} chunks verified")

            # If we have enough results, stop processing
            if len(verified_chunks) >= max_results:
                break

        if not verified_chunks:
            app.logger.info("No chunks passed verification")
            return _no_results_response()

        # Convert back to ChromaDB format
        result_ids = [chunk['id'] for chunk in verified_chunks]
        result_documents = [chunk['document'] for chunk in verified_chunks]
        result_distances = [chunk['distance'] for chunk in verified_chunks]
        result_metadatas = [chunk['metadata'] for chunk in verified_chunks]

        app.logger.info(f"Returning {len(verified_chunks)} verified chunks")

        return {
            'ids': [result_ids],
            'documents': [result_documents],
            'distances': [result_distances],
            'metadatas': [result_metadatas]
        }

    except Exception as e:
        app.logger.error(f"Batch processing failed: {e}")
        raise

def _no_results_response():
    """Return standardized no results response."""
    return {
        "status": "success",
        "message": "information not found from local source",
        "ids": [[]],
        "documents": [[]],
        "distances": [[]],
        "metadatas": [[]]
    }

def _intelligent_search_process(user_message, embedding, source_filter=None):
    """Execute the intelligent search process."""
    try:
        # Step 1: Extract keywords using Ollama
        app.logger.info("Step 1: Extracting keywords with Ollama")
        keywords = _extract_keywords_with_ollama(user_message)
        app.logger.info(f"Extracted keywords: {keywords}")

        # Step 2: Filter chunks by metadata
        app.logger.info("Step 2: Filtering chunks by metadata")
        filtered_chunks = _filter_chunks_by_metadata(keywords, source_filter)
        app.logger.info(f"Filtered {len(filtered_chunks)} chunks by metadata")

        if not filtered_chunks:
            app.logger.info("No chunks found after metadata filtering")
            return _no_results_response()

        # Step 3: Query with embedding on filtered chunks
        app.logger.info("Step 3: Querying filtered chunks with embedding")

        # Since ChromaDB might not support complex ID filtering in query,
        # we'll get all results and then filter by our chunk IDs
        chunk_ids_set = {chunk['id'] for chunk in filtered_chunks}

        # Query all documents with embedding similarity
        all_results = collection.query(
            query_embeddings=[embedding],
            n_results=min(len(filtered_chunks) * 2, 1000),  # Get more than needed, then filter
            include=["documents", "metadatas", "distances"]
        )

        # Filter results to only include our filtered chunks
        filtered_results = {
            'ids': [[]],
            'documents': [[]],
            'distances': [[]],
            'metadatas': [[]]
        }

        if all_results and all_results.get('ids') and all_results['ids'][0]:
            for i, result_id in enumerate(all_results['ids'][0]):
                if result_id in chunk_ids_set:
                    filtered_results['ids'][0].append(result_id)
                    filtered_results['documents'][0].append(
                        all_results['documents'][0][i] if i < len(all_results['documents'][0]) else ""
                    )
                    filtered_results['distances'][0].append(
                        all_results['distances'][0][i] if i < len(all_results['distances'][0]) else 1.0
                    )
                    filtered_results['metadatas'][0].append(
                        all_results['metadatas'][0][i] if i < len(all_results['metadatas'][0]) else {}
                    )

        embedding_results = filtered_results

        app.logger.info(f"Embedding query returned {len(embedding_results.get('ids', [[]])[0])} results")

        # Step 4: Verify chunks with Ollama
        app.logger.info("Step 4: Verifying chunks with Ollama")
        verified_results = _process_chunks_in_batches(
            embedding_results,
            user_message,
            INTELLIGENT_QUERY_CONFIG["batch_size"]
        )

        return verified_results

    except OllamaServiceError as e:
        app.logger.error(f"Ollama service error in intelligent search: {e}")
        raise
    except Exception as e:
        app.logger.error(f"Intelligent search process failed: {e}")
        raise

@app.route("/query", methods=["POST"])
def query_chroma():
    """Intelligent query with Ollama-powered semantic understanding and enhanced metadata filtering."""
    try:
        # Phase 1: Request Processing & Validation
        data = request.get_json(force=True)
        app.logger.info(f"Intelligent query request received, data type: {type(data)}")

        if isinstance(data, dict):
            app.logger.info(f"Data keys: {list(data.keys())}")
            # Log all data for debugging (truncate long values)
            for key, value in data.items():
                if key == "embedding":
                    app.logger.info(f"  {key}: [array with {len(value) if isinstance(value, list) else 'unknown'} elements]")
                else:
                    value_str = str(value)[:200] if len(str(value)) > 200 else str(value)
                    app.logger.info(f"  {key}: {value_str}")
        else:
            app.logger.info(f"Data content preview: {str(data)[:200]}...")

        # Extract parameters (removed adaptive_results)
        source_filter = data.get("source_filter")
        query_text = data.get("query_text", "")

        # Debug: Check all possible field names for the message
        app.logger.info(f"Checking for query text in request data:")
        app.logger.info(f"  query_text: '{data.get('query_text', 'NOT_FOUND')}'")
        app.logger.info(f"  query: '{data.get('query', 'NOT_FOUND')}'")
        app.logger.info(f"  message: '{data.get('message', 'NOT_FOUND')}'")
        app.logger.info(f"  text: '{data.get('text', 'NOT_FOUND')}'")
        app.logger.info(f"  user_message: '{data.get('user_message', 'NOT_FOUND')}'")
        app.logger.info(f"  question: '{data.get('question', 'NOT_FOUND')}'")

        # Check for alternative field names that n8n might use
        if not query_text:
            query_text = data.get("query", "")
        if not query_text:
            query_text = data.get("message", "")
        if not query_text:
            query_text = data.get("text", "")
        if not query_text:
            query_text = data.get("user_message", "")
        if not query_text:
            query_text = data.get("question", "")

        app.logger.info(f"Final extracted query_text: '{query_text}'")

        if source_filter:
            app.logger.info(f"Source filter provided: '{source_filter}'")

        # Parse embedding
        embedding = _parse_embedding_input(data)
        if isinstance(embedding, tuple):  # Error response
            app.logger.error("Failed to parse embedding input")
            return embedding

        app.logger.info(f"Embedding parsed successfully, dimension: {len(embedding)}")

        # Phase 2: Intelligent Query Routing
        detected_source, cleaned_message = _detect_source_pattern(query_text)
        if detected_source:
            # Case A: Source-specific query
            source_filter = detected_source
            query_text = cleaned_message
            app.logger.info(f"Case A: Source-specific query for '{source_filter}'")
        else:
            # Case B: General query
            app.logger.info("Case B: General query")

        # Validate source if specified
        if source_filter:
            try:
                if not _validate_source_exists(source_filter):
                    app.logger.warning(f"Source '{source_filter}' not found in database")
                    return jsonify({
                        "status": "error",
                        "error": f"Source '{source_filter}' not found in database",
                        "error_type": "source_not_found"
                    }), 404
            except Exception as e:
                app.logger.error(f"Error validating source filter '{source_filter}': {e}")
                return jsonify({
                    "status": "error",
                    "error": f"Error validating source: {str(e)}",
                    "error_type": "validation_error"
                }), 500

        # Phase 3: Smart Processing
        app.logger.info("Starting intelligent search process")
        results = _intelligent_search_process(query_text, embedding, source_filter)

        # Phase 4: Response & Logging
        result_count = len(results.get('ids', [[]])[0])
        app.logger.info(f"Intelligent query completed: {result_count} results")

        # Log top results for debugging (enhanced with new metadata)
        for i, doc_id in enumerate(results.get('ids', [[]])[0][:3]):  # Only log top 3
            document = results.get('documents', [[]])[0][i] if results.get('documents') else None
            distance = results.get('distances', [[]])[0][i] if results.get('distances') else None
            metadata = results.get('metadatas', [[]])[0][i] if results.get('metadatas') else None

            app.logger.info(f"Result {i+1}: ID={doc_id}, Distance={distance:.4f}")
            app.logger.info(f"  Source: {metadata.get('Source', 'N/A') if metadata else 'N/A'}")
            app.logger.info(f"  Title: {metadata.get('Title', 'N/A') if metadata else 'N/A'}")
            app.logger.info(f"  Remark: {metadata.get('Remark', 'N/A') if metadata else 'N/A'}")
            app.logger.info(f"  Document preview: {document[:150] if document else 'None'}...")

        # Phase 5: Cleanup
        _clear_request_cache()
        return jsonify(results)

    except OllamaServiceError as e:
        _clear_request_cache()
        app.logger.error(f"Ollama service error: {e}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "error_type": "ollama_service_error"
        }), 503
    except Exception as e:
        _clear_request_cache()
        app.logger.error(f"Intelligent query failed: {e}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "error_type": "internal_error"
        }), 500

@app.route("/search", methods=["POST"])
def enhanced_search():
    """
    Enhanced search endpoint with query preprocessing and intelligent result optimization.
    Supports both text queries and direct embedding queries.
    """
    try:
        data = request.get_json(force=True)

        # Extract parameters
        query_text = data.get("query", "")
        embedding = data.get("embedding")
        n_results = data.get("n_results", None)  # Let adaptive sizing determine if not specified
        include_metadata = data.get("include_metadata", True)

        app.logger.info(f"Enhanced search request: query='{query_text[:100]}...', has_embedding={embedding is not None}")

        # Parse query for source filtering
        source_filter, cleaned_query = _parse_query_for_source_filter(query_text)

        if source_filter:
            app.logger.info(f"Source filter detected: '{source_filter}', cleaned query: '{cleaned_query}'")
            query_text = cleaned_query

        # If no embedding provided, this endpoint expects the embedding to be generated externally
        if not embedding:
            return jsonify({
                "status": "error",
                "message": "This endpoint requires pre-generated embeddings. Use the /query endpoint for direct embedding queries."
            }), 400

        # Parse embedding
        parsed_embedding = _parse_embedding_input({"embedding": embedding})
        if isinstance(parsed_embedding, tuple):  # Error response
            return parsed_embedding

        # Apply query preprocessing enhancements
        enhanced_query_data = _preprocess_query_for_better_results(query_text, source_filter)

        # Determine optimal result count
        if n_results is None:
            n_results = _calculate_adaptive_result_count(query_text, source_filter, True)

        app.logger.info(f"Using result count: {n_results}")

        # Prepare query parameters
        query_params = {
            "query_embeddings": [parsed_embedding],
            "n_results": n_results
        }

        # Add source filtering if detected
        if source_filter and _validate_source_exists(source_filter):
            query_params["where"] = {"Source": source_filter}
            app.logger.info(f"Applied source filter: {source_filter}")
        elif source_filter:
            app.logger.warning(f"Source '{source_filter}' not found, searching all sources")
            source_filter = None

        # Execute query
        results = collection.query(**query_params)

        # Apply enhancements for unfiltered searches
        if not source_filter and query_text:
            results = _enhance_results_without_source_filter(results, query_text, parsed_embedding)

        # Add search metadata
        search_metadata = {
            "original_query": data.get("query", ""),
            "cleaned_query": query_text,
            "source_filter": source_filter,
            "result_count": len(results.get('ids', [[]])[0]),
            "adaptive_sizing_used": n_results != data.get("n_results"),
            "enhancements_applied": not source_filter and bool(query_text)
        }

        # Include metadata in response if requested
        if include_metadata:
            results["search_metadata"] = search_metadata

        app.logger.info(f"Enhanced search completed: {search_metadata['result_count']} results")
        return jsonify(results)

    except Exception as e:
        app.logger.error(f"Enhanced search failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/search_metadata", methods=["POST"])
def search_with_metadata_filters():
    """
    Enhanced search endpoint with support for filtering by Source, Title, and Remark metadata fields.
    Supports the new metadata structure: {"Source": "filename.pdf", "Title": "title", "Remark": "remark"}
    """
    try:
        data = request.get_json(force=True)

        # Extract parameters
        query_text = data.get("query", "")
        embedding = data.get("embedding")
        n_results = data.get("n_results", 15)
        include_metadata = data.get("include_metadata", True)

        # Extract metadata filters
        source_filter = data.get("source_filter")
        title_filter = data.get("title_filter")
        remark_filter = data.get("remark_filter")

        app.logger.info(f"Metadata search request: query='{query_text[:100]}...', "
                       f"source_filter='{source_filter}', title_filter='{title_filter}', "
                       f"remark_filter='{remark_filter}'")

        # Parse query for @filename.pdf prefix (backward compatibility)
        if not source_filter:
            parsed_source_filter, cleaned_query = _parse_query_for_source_filter(query_text)
            if parsed_source_filter:
                source_filter = parsed_source_filter
                query_text = cleaned_query
                app.logger.info(f"Extracted source filter from query: '{source_filter}'")

        # Validate embedding
        if not embedding:
            return jsonify({
                "status": "error",
                "message": "This endpoint requires pre-generated embeddings."
            }), 400

        parsed_embedding = _parse_embedding_input({"embedding": embedding})
        if isinstance(parsed_embedding, tuple):  # Error response
            return parsed_embedding

        # Build where clause for metadata filtering
        where_clause = _build_metadata_where_clause(source_filter, title_filter, remark_filter)

        # Prepare query parameters
        query_params = {
            "query_embeddings": [parsed_embedding],
            "n_results": n_results
        }

        # Add metadata filtering if any filters are specified
        if where_clause:
            query_params["where"] = where_clause
            app.logger.info(f"Applied metadata filters: {where_clause}")

        # Execute query
        results = collection.query(**query_params)

        # Apply enhancements for unfiltered searches
        if not where_clause and query_text:
            results = _enhance_results_without_source_filter(results, query_text, parsed_embedding)

        # Add search metadata
        search_metadata = {
            "original_query": data.get("query", ""),
            "cleaned_query": query_text,
            "filters_applied": {
                "source": source_filter,
                "title": title_filter,
                "remark": remark_filter
            },
            "result_count": len(results.get('ids', [[]])[0]),
            "metadata_filtering_used": bool(where_clause)
        }

        # Include metadata in response if requested
        if include_metadata:
            results["search_metadata"] = search_metadata

        app.logger.info(f"Metadata search completed: {search_metadata['result_count']} results")
        return jsonify(results)

    except Exception as e:
        app.logger.error(f"Metadata search failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

def _build_metadata_where_clause(source_filter, title_filter, remark_filter):
    """
    Build ChromaDB where clause for metadata filtering.

    Args:
        source_filter: Filter by Source field
        title_filter: Filter by Title field
        remark_filter: Filter by Remark field

    Returns:
        Dictionary representing the where clause, or None if no filters
    """
    where_conditions = {}

    if source_filter:
        where_conditions["Source"] = source_filter

    if title_filter:
        where_conditions["Title"] = title_filter

    if remark_filter:
        where_conditions["Remark"] = remark_filter

    return where_conditions if where_conditions else None

@app.route("/metadata_info", methods=["GET"])
def get_metadata_info():
    """
    Get information about available metadata fields and values in the collection.
    Useful for understanding what filters can be applied.
    """
    try:
        # Get a sample of documents to analyze metadata structure
        sample_results = collection.get(
            limit=100,  # Sample first 100 documents
            include=["metadatas"]
        )

        if not sample_results or not sample_results.get("metadatas"):
            return jsonify({
                "status": "success",
                "message": "No documents found in collection",
                "metadata_info": {
                    "total_documents": 0,
                    "available_fields": [],
                    "sources": [],
                    "titles": [],
                    "remarks": []
                }
            })

        metadatas = sample_results["metadatas"]

        # Analyze metadata structure
        sources = set()
        titles = set()
        remarks = set()
        all_fields = set()

        for metadata in metadatas:
            if isinstance(metadata, dict):
                all_fields.update(metadata.keys())

                if "Source" in metadata and metadata["Source"]:
                    sources.add(metadata["Source"])

                if "Title" in metadata and metadata["Title"]:
                    titles.add(metadata["Title"])

                if "Remark" in metadata and metadata["Remark"]:
                    remarks.add(metadata["Remark"])

        # Get total document count
        total_count = collection.count()

        metadata_info = {
            "total_documents": total_count,
            "sample_size": len(metadatas),
            "available_fields": sorted(list(all_fields)),
            "sources": sorted(list(sources))[:20],  # Limit to first 20
            "titles": sorted(list(titles))[:20],    # Limit to first 20
            "remarks": sorted(list(remarks))[:20],  # Limit to first 20
            "field_counts": {
                "sources": len(sources),
                "titles": len(titles),
                "remarks": len(remarks)
            }
        }

        app.logger.info(f"Metadata info request: {total_count} total documents, "
                       f"{len(sources)} unique sources, {len(titles)} unique titles, "
                       f"{len(remarks)} unique remarks")

        return jsonify({
            "status": "success",
            "metadata_info": metadata_info
        })

    except Exception as e:
        app.logger.error(f"Failed to get metadata info: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

def _preprocess_query_for_better_results(query_text, source_filter):
    """
    Preprocess query to improve search results through query expansion and analysis.

    Args:
        query_text (str): The cleaned query text
        source_filter (str): Source filter if specified

    Returns:
        dict: Enhanced query data with preprocessing insights
    """
    if not query_text:
        return {"original_query": query_text, "enhancements": []}

    enhancements = []

    # Analyze query characteristics
    word_count = len(query_text.split())
    has_technical_terms = any(term in query_text.lower() for term in
                             ['api', 'system', 'network', 'database', 'server', 'interface', 'protocol'])
    has_questions = any(word in query_text.lower() for word in
                       ['what', 'how', 'why', 'when', 'where', 'who', 'which'])

    if word_count <= 2:
        enhancements.append("short_query_detected")

    if has_technical_terms:
        enhancements.append("technical_query_detected")

    if has_questions:
        enhancements.append("question_query_detected")

    app.logger.info(f"Query preprocessing: {len(enhancements)} enhancements detected: {enhancements}")

    return {
        "original_query": query_text,
        "word_count": word_count,
        "enhancements": enhancements,
        "has_technical_terms": has_technical_terms,
        "has_questions": has_questions
    }

def _parse_query_for_source_filter(query):
    """
    Parse user query to extract source filter and clean query text.

    Args:
        query (str): User query that may start with @filename.pdf

    Returns:
        tuple: (source_filter, cleaned_query)
            - source_filter: filename if @filename.pdf prefix found, None otherwise
            - cleaned_query: query text with @filename.pdf prefix removed
    """
    if not query or not isinstance(query, str):
        return None, query

    query = query.strip()

    # Check if query starts with @filename pattern
    if query.startswith('@'):
        # Find the first space to separate the source from the rest of the query
        space_index = query.find(' ')
        if space_index > 1:  # Ensure there's content after @
            source_part = query[1:space_index]  # Remove @ prefix
            cleaned_query = query[space_index + 1:].strip()  # Remove source and leading spaces

            # Validate that source_part looks like a filename (contains a dot)
            if '.' in source_part:
                app.logger.info(f"Source filter extracted: '{source_part}', cleaned query: '{cleaned_query}'")
                return source_part, cleaned_query
            else:
                app.logger.warning(f"Invalid source format: '{source_part}' (no file extension)")
        else:
            app.logger.warning(f"Malformed source query: '{query}' (no space after source)")

    # No valid source filter found, return original query
    return None, query

def _calculate_adaptive_result_count(query_text, source_filter, adaptive_enabled=True):
    """
    Calculate optimal result count based on query characteristics and context.

    Args:
        query_text (str): The original query text for analysis
        source_filter (str): Source filter if specified
        adaptive_enabled (bool): Whether to use adaptive sizing

    Returns:
        int: Optimal number of results to retrieve
    """
    if not adaptive_enabled:
        return QUERY_ENHANCEMENT_CONFIG["default_result_count"]

    base_count = QUERY_ENHANCEMENT_CONFIG["default_result_count"]
    min_count = QUERY_ENHANCEMENT_CONFIG["min_result_count"]
    max_count = QUERY_ENHANCEMENT_CONFIG["max_result_count"]

    # If source filter is specified, we can be more focused
    if source_filter:
        return min(base_count, 10)  # Reduce for focused search

    # Analyze query characteristics for unfiltered search
    if not query_text:
        return base_count

    query_length = len(query_text.split())

    # Adjust based on query specificity
    if query_length <= 3:
        # Short queries are often broad - get more results for better coverage
        return min(base_count + 10, max_count)
    elif query_length <= 6:
        # Medium queries - standard count
        return base_count
    else:
        # Long, specific queries - fewer results needed
        return max(base_count - 5, min_count)

def _enhance_results_without_source_filter(results, query_text, embedding=None):
    """
    Apply post-processing enhancements when no source filter is specified.
    Includes diversity filtering, keyword matching, and result reranking.

    Args:
        results: ChromaDB query results
        query_text (str): Original query text
        embedding: Query embedding vector (optional, for future use)

    Returns:
        Enhanced results with improved relevance and diversity
    """
    try:
        if not results or not results.get('ids') or not results.get('ids')[0]:
            return results

        ids = results['ids'][0]
        documents = results.get('documents', [[]])[0]
        distances = results.get('distances', [[]])[0]
        metadatas = results.get('metadatas', [[]])[0]

        # Create enhanced result items
        enhanced_items = []
        for i in range(len(ids)):
            item = {
                'id': ids[i],
                'document': documents[i] if i < len(documents) else "",
                'distance': distances[i] if i < len(distances) else 1.0,
                'metadata': metadatas[i] if i < len(metadatas) else {},
                'relevance_score': 0.0,
                'keyword_score': 0.0,
                'diversity_penalty': 0.0
            }
            enhanced_items.append(item)

        # Apply keyword matching boost
        enhanced_items = _apply_keyword_matching(enhanced_items, query_text)

        # Apply source diversity
        enhanced_items = _apply_source_diversity(enhanced_items)

        # Calculate final relevance scores and rerank
        enhanced_items = _calculate_final_relevance_scores(enhanced_items)

        # Sort by final relevance score
        enhanced_items.sort(key=lambda x: x['relevance_score'], reverse=True)

        # Rebuild results structure
        enhanced_results = {
            'ids': [[item['id'] for item in enhanced_items]],
            'documents': [[item['document'] for item in enhanced_items]],
            'distances': [[item['distance'] for item in enhanced_items]],
            'metadatas': [[item['metadata'] for item in enhanced_items]]
        }

        app.logger.info(f"Enhanced {len(enhanced_items)} results with keyword matching and diversity")
        return enhanced_results

    except Exception as e:
        app.logger.error(f"Error in result enhancement: {e}")
        return results  # Return original results on error

def _validate_source_exists(source_filter):
    """
    Check if the specified source exists in the ChromaDB collection.

    Args:
        source_filter (str): The source filename to check

    Returns:
        bool: True if source exists, False otherwise
    """
    try:
        # Query for documents with the specified source
        test_results = collection.get(
            where={"Source": source_filter},
            limit=1,
            include=[]  # Only need to check existence, not content
        )

        exists = len(test_results.get("ids", [])) > 0
        app.logger.info(f"Source '{source_filter}' exists: {exists}")
        return exists

    except Exception as e:
        app.logger.error(f"Error checking if source '{source_filter}' exists: {e}")
        return False

def _apply_keyword_matching(enhanced_items, query_text):
    """
    Apply keyword matching boost to results based on exact term matches.

    Args:
        enhanced_items: List of enhanced result items
        query_text (str): Original query text

    Returns:
        List of items with keyword_score calculated
    """
    if not query_text:
        return enhanced_items

    # Extract meaningful keywords (remove common stop words)
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}

    query_keywords = [word.lower().strip('.,!?;:"()[]{}') for word in query_text.split()
                     if len(word) > 2 and word.lower() not in stop_words]

    if not query_keywords:
        return enhanced_items

    for item in enhanced_items:
        document_text = item['document'].lower()
        keyword_matches = 0
        total_keywords = len(query_keywords)

        for keyword in query_keywords:
            if keyword in document_text:
                keyword_matches += 1

        # Calculate keyword score (0.0 to 1.0)
        item['keyword_score'] = keyword_matches / total_keywords if total_keywords > 0 else 0.0

    return enhanced_items

def _apply_source_diversity(enhanced_items):
    """
    Apply diversity penalty to avoid over-representation from single sources.

    Args:
        enhanced_items: List of enhanced result items

    Returns:
        List of items with diversity_penalty calculated
    """
    # Count occurrences of each source
    source_counts = {}
    for item in enhanced_items:
        source = item['metadata'].get('Source', 'unknown')
        source_counts[source] = source_counts.get(source, 0) + 1

    # Apply diversity penalty based on source frequency
    for item in enhanced_items:
        source = item['metadata'].get('Source', 'unknown')
        source_frequency = source_counts.get(source, 1)

        # Penalty increases with frequency (logarithmic to avoid harsh penalties)
        max_penalty = QUERY_ENHANCEMENT_CONFIG["diversity_penalty_max"]
        if source_frequency > 1:
            item['diversity_penalty'] = min(max_penalty, 0.1 * (source_frequency - 1))
        else:
            item['diversity_penalty'] = 0.0

    return enhanced_items

def _calculate_final_relevance_scores(enhanced_items):
    """
    Calculate final relevance scores combining semantic similarity, keyword matching, and diversity.

    Args:
        enhanced_items: List of enhanced result items

    Returns:
        List of items with final relevance_score calculated
    """
    # Get weights from configuration
    keyword_weight = QUERY_ENHANCEMENT_CONFIG["keyword_matching_weight"]
    semantic_weight = QUERY_ENHANCEMENT_CONFIG["semantic_weight"]

    for item in enhanced_items:
        # Convert distance to similarity (lower distance = higher similarity)
        semantic_score = max(0.0, 1.0 - item['distance'])

        # Combine scores with configured weights
        base_score = (semantic_weight * semantic_score) + (keyword_weight * item['keyword_score'])

        # Apply diversity penalty
        final_score = max(0.0, base_score - item['diversity_penalty'])

        item['relevance_score'] = final_score

    return enhanced_items

def _parse_embedding_input(data):
    """Parse and validate embedding input from various formats."""
    embedding = None

    if isinstance(data, dict):
        embedding = data.get("embedding")
    elif isinstance(data, list):
        embedding = data
    else:
        return jsonify({"error": "Invalid data format"}), 400

    if not embedding:
        return jsonify({"error": "embedding required"}), 400

    # Handle string array format: "[Array: [0.08345413208007812,...]]"
    if isinstance(embedding, str) and embedding.startswith("[Array: [") and embedding.endswith("]]"):
        try:
            array_content = embedding[8:-2]
            embedding = [float(x.strip()) for x in array_content.split(',')]
            app.logger.info(f"Converted string array format ({len(embedding)} dimensions)")
        except (ValueError, IndexError) as e:
            app.logger.error(f"Failed to parse string array: {e}")
            return jsonify({"error": "Invalid array format in embedding"}), 400

    # Handle n8n string format: "[0.1, 0.2, 0.3, ...]" or "[-0.1, 0.2, -0.3, ...]"
    elif isinstance(embedding, str) and embedding.startswith("[") and embedding.endswith("]"):
        try:
            # Remove brackets and split by comma
            array_content = embedding[1:-1]
            embedding = [float(x.strip()) for x in array_content.split(',')]
            app.logger.info(f"Converted n8n string array format ({len(embedding)} dimensions)")
        except (ValueError, IndexError) as e:
            app.logger.error(f"Failed to parse n8n string array: {e}")
            return jsonify({"error": "Invalid n8n array format in embedding"}), 400

    # Handle comma-separated string format: "0.1,0.2,0.3,..." (no brackets)
    elif isinstance(embedding, str) and ',' in embedding:
        try:
            embedding = [float(x.strip()) for x in embedding.split(',')]
            app.logger.info(f"Converted comma-separated string format ({len(embedding)} dimensions)")
        except (ValueError, IndexError) as e:
            app.logger.error(f"Failed to parse comma-separated string: {e}")
            return jsonify({"error": "Invalid comma-separated format in embedding"}), 400

    if not isinstance(embedding, list):
        return jsonify({"error": "Embedding must be a list of numbers"}), 400

    return embedding

@app.route("/print_all", methods=["POST"])
def print_all():
    """Print all documents to console for debugging."""
    try:
        results = collection.get(include=["documents", "metadatas", "embeddings"])
        app.logger.info("Printing all documents in ChromaDB")

        for i in range(len(results["ids"])):
            print(f"ID: {results['ids'][i]}")
            print(f"Document: {results['documents'][i]}")
            print(f"Metadata: {results['metadatas'][i]}")
            print("-" * 40)

        return jsonify({"status": "ok", "total": len(results["ids"])})

    except Exception as e:
        app.logger.error(f"Print operation failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/delete_all", methods=["POST"])
def delete_all():
    """Delete all documents from the collection."""
    try:
        all_data = collection.get(include=[])
        ids = all_data.get("ids", [])

        if not ids:
            return jsonify({"status": "ok", "message": "No records to delete"})

        collection.delete(ids=ids)
        app.logger.info(f"Deleted {len(ids)} records from {COLLECTION_NAME}")
        return jsonify({"status": "ok", "message": f"Deleted {len(ids)} items"})

    except Exception as e:
        app.logger.error(f"Delete operation failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Web Interface
@app.route("/", methods=["GET"])
def web_interface():
    """Serve the web management interface."""
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return """
        <h1>Error: index.html not found</h1>
        <p>Please make sure index.html exists in the project directory.</p>
        """

# API Endpoints for Web Interface
@app.route("/api/stats", methods=["GET"])
def get_stats():
    """Get collection statistics."""
    try:
        all_data = collection.get(include=[])
        total_docs = len(all_data.get("ids", []))

        return jsonify({
            "status": "ok",
            "total_documents": total_docs,
            "collection_name": COLLECTION_NAME
        })
    except Exception as e:
        app.logger.error(f"Stats operation failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Callback System for Chunk Processing
def _send_next_chunk_with_delay(session_id, chunk_info, delay_seconds):
    """Send the next chunk to n8n after a specified delay for GPU rest."""
    try:
        chunk_text, chunk_filename = chunk_info

        # Wait for the specified delay to give GPU time to rest
        app.logger.info(f"Waiting {delay_seconds}s before sending next chunk for session {session_id}")
        time.sleep(delay_seconds)

        app.logger.info(f"Sending next chunk for session {session_id}: {chunk_filename}")

        with queue_lock:
            processing_status[session_id]['current_chunk'] = chunk_filename
            processing_status[session_id]['status'] = 'processing'
            processing_status[session_id]['last_update'] = datetime.now()

        # Extract chunk number from filename (format: filename_chunk_N)
        chunk_number = None
        if '_chunk_' in chunk_filename:
            try:
                chunk_number = int(chunk_filename.split('_chunk_')[-1])
            except (ValueError, IndexError):
                app.logger.warning(f"Could not extract chunk number from {chunk_filename}")

        # Extract original filename from chunk_filename for source consistency
        original_filename = chunk_filename.split("_chunk_")[0] if "_chunk_" in chunk_filename else chunk_filename
        success = _send_chunk_to_n8n(chunk_text, chunk_filename, session_id, chunk_number, original_filename=original_filename)

        if not success:
            app.logger.error(f"Failed to send chunk {chunk_filename} for session {session_id}")
            with queue_lock:
                processing_status[session_id]['status'] = 'error'
                processing_status[session_id]['error'] = f"Failed to send chunk {chunk_filename}"

    except Exception as e:
        app.logger.error(f"Error in _send_next_chunk_with_delay: {str(e)}")
        with queue_lock:
            processing_status[session_id]['status'] = 'error'
            processing_status[session_id]['error'] = str(e)

# Note: _send_next_chunk_async removed - use _send_next_chunk_with_delay instead

@app.route('/chunk_complete', methods=['POST'])
def chunk_processing_complete():
    """Handle chunk processing completion callback from n8n."""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        status = data.get('status', 'success')
        chunk_id = data.get('chunk_id')
        app.logger.info(f"Received chunk completion callback: session_id={session_id}, chunk_id={chunk_id}, status={status}")

        # Check if this is a Plan B session that should be redirected
        if session_id and session_id.startswith('planb_'):
            app.logger.info(f"Redirecting Plan B session {session_id} to plan_b_piece_complete endpoint")
            # Forward to Plan B piece complete handler
            return plan_b_piece_complete()

        with queue_lock:
            if session_id not in processing_status:
                app.logger.warning(f"Unknown session_id: {session_id}")
                return jsonify({"error": "Unknown session_id"}), 404

            processing_status[session_id]['last_completed'] = data.get('chunk_id')
            processing_status[session_id]['last_status'] = status
            processing_status[session_id]['last_update'] = datetime.now()

            if session_id in chunk_queues and not chunk_queues[session_id].empty():
                next_chunk_info = chunk_queues[session_id].get()
                app.logger.info(f"Scheduling next chunk for session {session_id} with {GPU_REST_DELAY}s delay")

                # Add delay before sending next chunk to give GPU time to rest
                threading.Thread(
                    target=_send_next_chunk_with_delay,
                    args=(session_id, next_chunk_info, GPU_REST_DELAY),
                    daemon=True
                ).start()
            else:
                processing_status[session_id]['status'] = 'completed'
                app.logger.info(f"All chunks processed for session {session_id}")

        return jsonify({"status": "ok", "message": "Chunk completion acknowledged"}), 200

    except Exception as e:
        app.logger.error(f"Error in chunk completion callback: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/plan_b_piece_complete', methods=['POST'])
def plan_b_piece_complete():
    """Handle Plan B piece completion and trigger second piece if needed."""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        status = data.get('status', 'success')

        with queue_lock:
            if session_id not in plan_b_sessions:
                app.logger.warning(f"Unknown Plan B session_id: {session_id}")
                return jsonify({"error": "Unknown Plan B session_id"}), 404

            plan_b_session = plan_b_sessions[session_id]
            plan_b_session['pieces_completed'] += 1
            total_pieces = plan_b_session['total_pieces']
            current_piece = plan_b_session['pieces_completed']  # Use completion count as piece number

        app.logger.info(f"Plan B piece {current_piece} completion callback: session_id={session_id}, status={status}, total_pieces={total_pieces}")
        app.logger.info(f"Plan B session state: pieces_completed={current_piece}, total_pieces={total_pieces}")

        with queue_lock:
            if current_piece == 1 and total_pieces > 1:
                # First piece completed, schedule second piece with delay
                app.logger.info(f"First piece completed for Plan B session {session_id}, scheduling second piece with {GPU_REST_DELAY}s delay")

                def send_next_piece_delayed(next_piece_number):
                    try:
                        time.sleep(GPU_REST_DELAY)

                        session_data = {
                            'session_id': session_id,
                            'chunk_number': f"{plan_b_session['original_chunk_number']}_piece_{next_piece_number}",
                            'source': plan_b_session['source']
                        }

                        # Get the appropriate piece
                        if next_piece_number == 2:
                            piece_text = plan_b_session['second_piece']
                        elif next_piece_number == 3:
                            piece_text = plan_b_session['third_piece']
                        else:
                            app.logger.error(f"Invalid piece number: {next_piece_number}")
                            return

                        success = _send_chunk_to_onfail_webhook(piece_text, session_data, next_piece_number)

                        with queue_lock:
                            if success:
                                plan_b_sessions[session_id]['pieces_sent'] = next_piece_number
                                app.logger.info(f"Piece {next_piece_number} sent successfully for Plan B session {session_id}")
                            else:
                                plan_b_sessions[session_id]['status'] = 'error'
                                app.logger.error(f"Failed to send piece {next_piece_number} for Plan B session {session_id}")

                    except Exception as e:
                        app.logger.error(f"Error sending piece {next_piece_number} for Plan B session {session_id}: {str(e)}")
                        with queue_lock:
                            plan_b_sessions[session_id]['status'] = 'error'

                # Start thread to send second piece after delay
                threading.Thread(target=lambda: send_next_piece_delayed(2), daemon=True).start()

            elif current_piece == 2 and total_pieces == 3:
                # Second piece completed, schedule third piece with delay
                app.logger.info(f"Second piece completed for Plan B session {session_id}, scheduling third piece with {GPU_REST_DELAY}s delay")

                def send_third_piece_delayed():
                    try:
                        time.sleep(GPU_REST_DELAY)

                        session_data = {
                            'session_id': session_id,
                            'chunk_number': f"{plan_b_session['original_chunk_number']}_piece_3",
                            'source': plan_b_session['source']
                        }

                        success = _send_chunk_to_onfail_webhook(
                            plan_b_session['third_piece'],
                            session_data,
                            3
                        )

                        with queue_lock:
                            if success:
                                plan_b_sessions[session_id]['pieces_sent'] = 3
                                app.logger.info(f"Third piece sent successfully for Plan B session {session_id}")
                            else:
                                plan_b_sessions[session_id]['status'] = 'error'
                                app.logger.error(f"Failed to send third piece for Plan B session {session_id}")

                    except Exception as e:
                        app.logger.error(f"Error sending third piece for Plan B session {session_id}: {str(e)}")
                        with queue_lock:
                            plan_b_sessions[session_id]['status'] = 'error'

                # Start thread to send third piece after delay
                threading.Thread(target=send_third_piece_delayed, daemon=True).start()

            elif current_piece == total_pieces:
                # All pieces completed, trigger continuation of original workflow
                app.logger.info(f"Plan B processing completed for session {session_id} ({total_pieces} pieces), triggering continuation")

                # Send completion signal back to original workflow to continue with next chunk
                original_session_id = plan_b_session['original_session_id']
                original_chunk_number = plan_b_session['original_chunk_number']

                def trigger_workflow_continuation():
                    try:
                        time.sleep(1)  # Small delay before triggering continuation

                        # Call chunk_complete endpoint to continue original workflow
                        continuation_payload = {
                            "session_id": original_session_id,
                            "chunk_id": original_chunk_number,
                            "status": "success",
                            "plan_b_completed": True,
                            "plan_b_pieces": total_pieces
                        }

                        response = requests.post(
                            "http://localhost:5555/chunk_complete",
                            json=continuation_payload,
                            headers={'Content-Type': 'application/json'},
                            timeout=30
                        )

                        if response.status_code == 200:
                            app.logger.info(f"Successfully triggered continuation for original session {original_session_id}")
                        else:
                            app.logger.error(f"Failed to trigger continuation: {response.status_code}")

                    except Exception as e:
                        app.logger.error(f"Error triggering workflow continuation: {str(e)}")

                # Clean up Plan B session
                plan_b_sessions[session_id]['status'] = 'completed'

                # Start thread to trigger continuation
                threading.Thread(target=trigger_workflow_continuation, daemon=True).start()

        return jsonify({"status": "ok", "message": "Plan B piece completion acknowledged"}), 200

    except Exception as e:
        app.logger.error(f"Error in Plan B piece completion callback: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/chunk_status/<session_id>', methods=['GET'])
def get_chunk_status(session_id):
    """Get the current status of chunk processing for a session."""
    try:
        with queue_lock:
            if session_id not in processing_status:
                return jsonify({"error": "Session not found"}), 404

            status_info = processing_status[session_id].copy()
            queue_size = chunk_queues[session_id].qsize() if session_id in chunk_queues else 0
            status_info['remaining_chunks'] = queue_size

            if 'last_update' in status_info and status_info['last_update']:
                status_info['last_update'] = status_info['last_update'].isoformat()

        return jsonify({
            "status": "ok",
            "session_id": session_id,
            "processing_info": status_info
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting chunk status: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/plan_b_status/<session_id>', methods=['GET'])
def get_plan_b_status(session_id):
    """Get the current status of Plan B processing for a session."""
    try:
        with queue_lock:
            if session_id not in plan_b_sessions:
                return jsonify({"error": "Plan B session not found"}), 404

            status_info = plan_b_sessions[session_id].copy()

            # Convert datetime to string for JSON serialization
            if 'created_at' in status_info and status_info['created_at']:
                status_info['created_at'] = status_info['created_at'].isoformat()

        return jsonify({
            "status": "ok",
            "plan_b_session_id": session_id,
            "processing_info": status_info
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting Plan B status: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500

@app.route("/api/documents", methods=["GET"])
def get_all_documents():
    """Get all documents with pagination support."""
    try:
        results = collection.get(include=["documents", "metadatas"])

        app.logger.info(f"ChromaDB get() returned: {type(results)}")
        app.logger.info(f"Results keys: {list(results.keys()) if isinstance(results, dict) else 'Not a dict'}")

        # Handle case where collection is empty
        if not results or not results.get("ids"):
            app.logger.info("Collection is empty, returning empty document list")
            return jsonify({
                "status": "ok",
                "documents": [],
                "total": 0
            })

        ids = results.get("ids", [])
        documents_content = results.get("documents", [])
        metadatas = results.get("metadatas", [])

        app.logger.info(f"Found {len(ids)} documents in collection")

        documents = []
        for i in range(len(ids)):
            doc = {
                "id": ids[i],
                "document": documents_content[i] if documents_content and i < len(documents_content) else None,
                "metadata": metadatas[i] if metadatas and i < len(metadatas) else {}
            }
            documents.append(doc)

        app.logger.info(f"Returning {len(documents)} documents")
        return jsonify({
            "status": "ok",
            "documents": documents,
            "total": len(documents)
        })

    except Exception as e:
        app.logger.error(f"Get documents failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/api/add-document", methods=["POST"])
def add_document_api():
    """Add a single document (enhanced version of /add)."""
    try:
        data = request.get_json(force=True)

        ids = data.get("ids", [])
        documents = data.get("documents", [])
        metadatas = data.get("metadatas", [])
        embeddings = data.get("embeddings")

        if not (ids and documents):
            return jsonify({"status": "error", "error": "ids and documents required"}), 400

        add_params = {
            "ids": ids,
            "documents": documents,
            "metadatas": metadatas
        }

        if embeddings:
            add_params["embeddings"] = embeddings

        collection.add(**add_params)

        app.logger.info(f"Added document(s): {ids}")
        return jsonify({"status": "ok", "inserted": len(ids), "ids": ids})

    except Exception as e:
        app.logger.error(f"Add document failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/api/delete-document", methods=["POST"])
def delete_document_api():
    """Delete specific documents by IDs."""
    try:
        data = request.get_json(force=True)
        ids = data.get("ids", [])

        if not ids:
            return jsonify({"status": "error", "error": "ids required"}), 400

        collection.delete(ids=ids)
        app.logger.info(f"Deleted documents: {ids}")
        return jsonify({"status": "ok", "deleted": len(ids), "ids": ids})

    except Exception as e:
        app.logger.error(f"Delete document failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/api/search", methods=["POST"])
def search_documents():
    """Search documents by text query using Ollama embeddings with optional source filtering."""
    try:
        data = request.get_json(force=True)
        original_query = data.get("query", "")
        n_results = data.get("n_results", 10)

        if not original_query:
            return jsonify({"status": "error", "error": "query required"}), 400

        # Parse query for source filter
        source_filter, cleaned_query = _parse_query_for_source_filter(original_query)

        app.logger.info(f"Original query: '{original_query}'")
        if source_filter:
            app.logger.info(f"Source filter: '{source_filter}', cleaned query: '{cleaned_query}'")
        else:
            app.logger.info("No source filter detected, searching all documents")

        # Generate embedding for the cleaned query (without source prefix)
        query_for_embedding = cleaned_query if source_filter else original_query
        app.logger.info(f"Generating embedding for query: '{query_for_embedding}'")

        ollama_payload = {"model": "snowflake-artic-embed2:568m", "prompt": query_for_embedding} # "all-minilm:33m" or "snowflake-artic-embed2:568m"

        ollama_response = requests.post(
            "http://localhost:11434/api/embeddings",
            json=ollama_payload,
            timeout=30
        )

        if ollama_response.status_code != 200:
            app.logger.error(f"Ollama embedding failed: {ollama_response.status_code}")
            return jsonify({"status": "error", "error": "Failed to generate embedding"}), 500

        embedding_data = ollama_response.json()
        query_embedding = embedding_data.get("embedding")

        if not query_embedding:
            return jsonify({"status": "error", "error": "No embedding returned from Ollama"}), 500

        # Prepare query parameters
        query_params = {
            "query_embeddings": [query_embedding],
            "n_results": n_results,
            "include": ["documents", "metadatas", "distances"]
        }

        # Add source filtering if specified
        if source_filter:
            try:
                # Validate source exists before applying filter
                if _validate_source_exists(source_filter):
                    # Filter by Source metadata field
                    query_params["where"] = {"Source": source_filter}
                    app.logger.info(f"Applying source filter: Source = '{source_filter}'")
                else:
                    app.logger.warning(f"Source '{source_filter}' not found in database. Falling back to search all documents")
                    source_filter = None  # Clear source filter for logging purposes
            except Exception as e:
                app.logger.error(f"Error validating source filter '{source_filter}': {e}")
                app.logger.info("Falling back to search all documents")
                source_filter = None  # Clear source filter for logging purposes

        results = collection.query(**query_params)

        # Log results summary
        result_count = len(results.get('ids', [[]])[0]) if results.get('ids') else 0
        if source_filter:
            app.logger.info(f"Source-filtered query returned {result_count} results from '{source_filter}'")
        else:
            app.logger.info(f"Query returned {result_count} results from all sources")

        # Add metadata to response for debugging
        response_data = dict(results)
        response_data["search_metadata"] = {
            "original_query": original_query,
            "cleaned_query": query_for_embedding,
            "source_filter": source_filter,
            "result_count": result_count
        }

        return jsonify(response_data)

    except requests.exceptions.RequestException as e:
        app.logger.error(f"Ollama API request failed: {e}")
        return jsonify({"status": "error", "error": "Ollama API unavailable"}), 500
    except Exception as e:
        app.logger.error(f"Search failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/api/collection-info", methods=["GET"])
def get_collection_info():
    """Get detailed collection information."""
    try:
        all_data = collection.get(include=[])
        count = len(all_data.get("ids", []))

        return jsonify({
            "status": "ok",
            "name": COLLECTION_NAME,
            "count": count,
            "client_type": "PersistentClient",
            "path": "./chroma_data"
        })
    except Exception as e:
        app.logger.error(f"Collection info failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/api/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    try:
        collection.get(limit=1)
        return jsonify({
            "status": "ok",
            "message": "ChromaDB connection is healthy",
            "collection": COLLECTION_NAME
        })
    except Exception as e:
        app.logger.error(f"Health check failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route("/api/export", methods=["GET"])
def export_data():
    """Export all data as JSON."""
    try:
        results = collection.get(include=["documents", "metadatas", "embeddings"])

        export_data = {
            "collection_name": COLLECTION_NAME,
            "export_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_documents": len(results["ids"]),
            "documents": [
                {
                    "id": results["ids"][i],
                    "document": results["documents"][i] if results["documents"] else None,
                    "metadata": results["metadatas"][i] if results["metadatas"] else {},
                    "embedding": results["embeddings"][i] if results["embeddings"] else None
                }
                for i in range(len(results["ids"]))
            ]
        }

        return jsonify(export_data)

    except Exception as e:
        app.logger.error(f"Export failed: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Configuration endpoints
@app.route("/api/config/gpu-delay", methods=["GET"])
def get_gpu_delay():
    """Get current GPU rest delay configuration."""
    global GPU_REST_DELAY
    return jsonify({
        "status": "ok",
        "gpu_rest_delay_seconds": GPU_REST_DELAY,
        "description": "Delay between chunk processing to allow GPU rest"
    })

@app.route("/api/config/gpu-delay", methods=["POST"])
def set_gpu_delay():
    """Set GPU rest delay configuration."""
    global GPU_REST_DELAY
    try:
        data = request.get_json(force=True)
        new_delay = data.get("delay_seconds")

        if new_delay is None:
            return create_error_response("delay_seconds parameter required", 400, "validation_error")

        if not isinstance(new_delay, (int, float)) or new_delay < 0:
            return create_error_response("delay_seconds must be a non-negative number", 400, "validation_error")

        old_delay = GPU_REST_DELAY
        GPU_REST_DELAY = float(new_delay)

        app.logger.info(f"GPU rest delay updated from {old_delay}s to {GPU_REST_DELAY}s")

        return create_success_response({
            "old_delay_seconds": old_delay,
            "new_delay_seconds": GPU_REST_DELAY
        }, f"GPU rest delay updated to {GPU_REST_DELAY} seconds")

    except Exception as e:
        return log_and_return_error(
            f"Failed to update GPU delay: {e}",
            "Failed to update GPU rest delay configuration"
        )

@app.route("/api/config/onfail-webhook", methods=["GET"])
def get_onfail_webhook():
    """Get current OnFail webhook URL configuration."""
    global N8N_ONFAIL_WEBHOOK_URL
    return jsonify({
        "status": "ok",
        "onfail_webhook_url": N8N_ONFAIL_WEBHOOK_URL,
        "description": "Webhook URL for OnFail workflow when Plan B processing is triggered"
    })

@app.route("/api/config/onfail-webhook", methods=["POST"])
def set_onfail_webhook():
    """Set OnFail webhook URL configuration."""
    global N8N_ONFAIL_WEBHOOK_URL
    try:
        data = request.get_json(force=True)
        new_url = data.get("webhook_url")

        if not new_url:
            return create_error_response("webhook_url parameter required", 400, "validation_error")

        if not isinstance(new_url, str):
            return create_error_response("webhook_url must be a string", 400, "validation_error")

        old_url = N8N_ONFAIL_WEBHOOK_URL
        N8N_ONFAIL_WEBHOOK_URL = new_url

        app.logger.info(f"OnFail webhook URL updated from '{old_url}' to '{N8N_ONFAIL_WEBHOOK_URL}'")

        return create_success_response({
            "old_webhook_url": old_url,
            "new_webhook_url": N8N_ONFAIL_WEBHOOK_URL
        }, f"OnFail webhook URL updated successfully")

    except Exception as e:
        return log_and_return_error(
            f"Failed to update OnFail webhook URL: {e}",
            "Failed to update OnFail webhook URL configuration"
        )

# Application entry point
if __name__ == "__main__":
    app.run(port=5555, host="0.0.0.0", debug=False)
