"""
Intelligent query processing with Ollama integration.
"""

import hashlib
import concurrent.futures
import requests
import re
from ..core.config import INTELLIGENT_QUERY_CONFIG
from ..core.exceptions import OllamaServiceError
from ..core.state import cache_lock, keyword_cache, verification_cache
from ..core.database import collection


def _get_cache_key(data):
    """Generate cache key from data."""
    return hashlib.md5(str(data).encode()).hexdigest()


def _cache_keywords(message, keywords):
    """Cache extracted keywords."""
    with cache_lock:
        cache_key = _get_cache_key(message)
        keyword_cache[cache_key] = keywords


def _get_cached_keywords(message):
    """Retrieve cached keywords."""
    with cache_lock:
        cache_key = _get_cache_key(message)
        return keyword_cache.get(cache_key)


def _cache_verification(message, chunk_content, confidence):
    """Cache chunk verification result."""
    with cache_lock:
        cache_key = _get_cache_key(f"{message}|{chunk_content}")
        verification_cache[cache_key] = confidence


def _get_cached_verification(message, chunk_content):
    """Retrieve cached verification result."""
    with cache_lock:
        cache_key = _get_cache_key(f"{message}|{chunk_content}")
        return verification_cache.get(cache_key)


def _clear_request_cache():
    """Clear all caches after request completion."""
    with cache_lock:
        keyword_cache.clear()
        verification_cache.clear()


def _call_ollama(prompt, timeout=None):
    """Call Ollama API with error handling and timeout."""
    if timeout is None:
        timeout = INTELLIGENT_QUERY_CONFIG["ollama_timeout"]

    try:
        response = requests.post(
            f"{INTELLIGENT_QUERY_CONFIG['ollama_url']}/api/generate",
            json={
                "model": INTELLIGENT_QUERY_CONFIG["ollama_model"],
                "prompt": prompt,
                "stream": False
            },
            timeout=timeout
        )

        if response.status_code != 200:
            raise OllamaServiceError(f"Ollama API returned status {response.status_code}: {response.text}")

        result = response.json()
        if "response" not in result:
            raise OllamaServiceError("Invalid response format from Ollama")

        return result["response"].strip()

    except requests.exceptions.Timeout:
        raise OllamaServiceError(f"Ollama request timed out after {timeout} seconds")
    except requests.exceptions.ConnectionError:
        raise OllamaServiceError("Cannot connect to Ollama service")
    except requests.exceptions.RequestException as e:
        raise OllamaServiceError(f"Ollama request failed: {str(e)}")
    except Exception as e:
        raise OllamaServiceError(f"Unexpected error calling Ollama: {str(e)}")


def _extract_keywords_with_ollama(user_message):
    """Extract and correct keywords from user message using Ollama."""
    import logging
    logger = logging.getLogger(__name__)

    # Handle empty or None message
    if not user_message or not user_message.strip():
        logger.info("Empty user message, returning empty keywords")
        return []

    # Check cache first
    cached_keywords = _get_cached_keywords(user_message)
    if cached_keywords:
        logger.info(f"Using cached keywords: {cached_keywords}")
        return cached_keywords

    try:
        logger.info(f"Extracting keywords from: '{user_message}'")
        prompt = INTELLIGENT_QUERY_CONFIG["keyword_extraction_prompt"].format(user_message=user_message)
        logger.info(f"Calling Ollama with prompt: {prompt[:100]}...")
        response = _call_ollama(prompt)

        # Filter out thinking process from logs - only log the actual keywords
        if "<think>" in response and "</think>" in response:
            # Extract only the part after </think>
            clean_response = response.split("</think>")[-1].strip()
            logger.info(f"Ollama keywords response: {clean_response}")
        else:
            logger.info(f"Ollama response: {response[:200]}...")  # Limit length if no think tags

        # Parse keywords from response - handle phi4-mini-reasoning output
        lines = response.strip().split('\n')
        keywords = []

        # Method 1: Look for lines that start with "Key phrases:" (our prompt format)
        for line in lines:
            line = line.strip()
            if line.startswith("Key phrases:"):
                # Extract everything after "Key phrases:"
                keyword_part = line.replace("Key phrases:", "").strip()
                if keyword_part and ',' in keyword_part:
                    potential_keywords = [kw.strip() for kw in keyword_part.split(',') if kw.strip()]
                    # Filter reasonable keywords
                    filtered_keywords = [kw for kw in potential_keywords if len(kw) < 50 and kw]
                    if filtered_keywords:
                        keywords = filtered_keywords[:4]  # Take first 4
                        break

        # Method 2: Look for clean comma-separated lines (fallback)
        if not keywords:
            for line in lines:
                line = line.strip()
                # Skip obvious non-keyword lines
                if (line and ',' in line and
                    not line.startswith('<') and
                    not line.startswith('Here') and
                    not line.startswith('The') and
                    not line.startswith('However') and
                    not line.startswith('Based') and
                    not line.startswith('But') and
                    not line.startswith('Alternatively') and
                    not 'approach' in line.lower() and
                    not 'extract' in line.lower() and
                    not 'phrase' in line.lower() and
                    not 'example' in line.lower() and
                    not 'maybe' in line.lower() and
                    len(line) < 150):  # Reasonable length for keyword list

                    # Split and clean keywords
                    potential_keywords = [kw.strip() for kw in line.split(',') if kw.strip()]

                    # Filter out explanatory phrases
                    filtered_keywords = []
                    for kw in potential_keywords:
                        if (len(kw) < 50 and
                            not any(word in kw.lower() for word in [
                                'however', 'approach', 'request', 'extraction', 'refined',
                                'adhering', 'initial', 'phrase', 'example', 'maybe',
                                'alternatively', 'combining', 'necessary'
                            ])):
                            filtered_keywords.append(kw)

                    if filtered_keywords and len(filtered_keywords) <= 6:
                        keywords = filtered_keywords[:4]
                        break

        # Method 3: Extract from first clean line (last resort)
        if not keywords:
            for line in lines[:3]:  # Check first 3 lines only
                line = line.strip()
                if line and ',' in line and len(line) < 100:
                    potential_keywords = [kw.strip() for kw in line.split(',') if kw.strip() and len(kw) < 30]
                    if potential_keywords and len(potential_keywords) <= 6:
                        keywords = potential_keywords[:4]
                        break

        # Cache the result
        _cache_keywords(user_message, keywords)

        logger.info(f"Final extracted keywords: {keywords}")
        return keywords

    except OllamaServiceError as e:
        logger.error(f"Ollama service error during keyword extraction: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during keyword extraction: {e}")
        raise OllamaServiceError(f"Keyword extraction failed: {str(e)}")


def _verify_chunk_relevance(user_message, chunk_content):
    """Verify chunk relevance using Ollama, returns confidence score."""
    # Check cache first
    cached_confidence = _get_cached_verification(user_message, chunk_content)
    if cached_confidence is not None:
        return cached_confidence

    try:
        prompt = INTELLIGENT_QUERY_CONFIG["chunk_verification_prompt"].format(
            user_message=user_message,
            chunk_content=chunk_content[:1000]  # Limit chunk size for efficiency
        )
        response = _call_ollama(prompt)

        # Parse confidence score - handle various response formats
        try:
            # Clean the response first
            cleaned_response = response.strip()

            # Extract the first number from the response (handle "0.75, defaulting to 0.0" format)
            numbers = re.findall(r'\b0\.\d{2}\b|\b1\.00\b|\b[01]\b', cleaned_response)

            if numbers:
                confidence = float(numbers[0])  # Take the first valid number
            else:
                # Try to parse the entire response as float
                confidence = float(cleaned_response)

            # Validate range
            if not (0.0 <= confidence <= 1.0):
                confidence = 0.0

        except ValueError:
            confidence = 0.0

        # Cache the result
        _cache_verification(user_message, chunk_content, confidence)

        return confidence

    except OllamaServiceError as e:
        raise
    except Exception as e:
        raise OllamaServiceError(f"Chunk verification failed: {str(e)}")


def _detect_source_pattern(message):
    """Detect @filename.pdf pattern in message, return (source, cleaned_message)."""
    if not message or not isinstance(message, str):
        return None, message

    message = message.strip()

    # Check if message starts with @filename pattern
    if message.startswith('@'):
        # Find the first space to separate the source from the rest of the query
        space_index = message.find(' ')
        if space_index > 1:  # Ensure there's content after @
            source_part = message[1:space_index]  # Remove @ prefix
            cleaned_message = message[space_index + 1:].strip()  # Remove source and leading spaces

            # Validate that source_part looks like a filename (contains a dot)
            if '.' in source_part:
                return source_part, cleaned_message

    # No valid source filter found, return original message
    return None, message


def _filter_chunks_by_metadata(keywords, source_filter=None):
    """Filter chunks where Title OR Remark contains keywords using Python filtering."""
    try:
        # Get all documents first (ChromaDB doesn't support text contains operations)
        if source_filter:
            # Filter by source first
            results = collection.get(
                where={"Source": source_filter},
                include=["metadatas", "documents"]
            )
        else:
            # Get all documents
            results = collection.get(include=["metadatas", "documents"])

        # Convert to list of chunk dictionaries
        all_chunks = []
        if results and results.get("ids"):
            for i, chunk_id in enumerate(results["ids"]):
                all_chunks.append({
                    "id": chunk_id,
                    "document": results["documents"][i] if i < len(results["documents"]) else "",
                    "metadata": results["metadatas"][i] if i < len(results["metadatas"]) else {}
                })

        # If no keywords, return all chunks (filtered by source if specified)
        if not keywords:
            return all_chunks

        # Filter chunks by keywords with scoring
        filtered_chunks = []
        for chunk in all_chunks:
            metadata = chunk["metadata"]
            title = metadata.get("Title", "").lower()
            remark = metadata.get("Remark", "").lower()
            document_content = chunk["document"].lower()

            # Calculate keyword relevance score
            keyword_score = _calculate_keyword_relevance_score(
                keywords, title, remark, document_content
            )

            if keyword_score > 0:  # At least one keyword found
                chunk["keyword_score"] = keyword_score
                filtered_chunks.append(chunk)

        # Sort by keyword relevance score (highest first)
        filtered_chunks.sort(key=lambda x: x["keyword_score"], reverse=True)

        return filtered_chunks

    except Exception as e:
        raise


def _calculate_keyword_relevance_score(keywords, title, remark, document_content):
    """Calculate relevance score based on keyword matches in different fields."""
    score = 0.0

    for keyword in keywords:
        keyword_lower = keyword.lower().strip()
        if not keyword_lower:
            continue

        # Title matches get highest weight (3x)
        if keyword_lower in title:
            score += 3.0

        # Remark matches get medium weight (2x)
        if keyword_lower in remark:
            score += 2.0

        # Document content matches get base weight (1x)
        if keyword_lower in document_content:
            score += 1.0

    # Bonus for multiple keyword matches
    total_keywords = len([k for k in keywords if k.strip()])
    if total_keywords > 1:
        # Count how many different keywords were found
        found_keywords = 0
        for keyword in keywords:
            keyword_lower = keyword.lower().strip()
            if keyword_lower and (keyword_lower in title or keyword_lower in remark or keyword_lower in document_content):
                found_keywords += 1

        # Bonus: 0.5 points for each additional keyword found
        if found_keywords > 1:
            score += (found_keywords - 1) * 0.5

    return score


def _count_keyword_matches_in_chunk(keywords, title, remark, document_content):
    """Count how many different keywords are found in a chunk (substring matching)."""
    found_keywords = 0
    for keyword in keywords:
        keyword_lower = keyword.lower().strip()
        if not keyword_lower:
            continue

        # Check if keyword exists in any field
        if (keyword_lower in title.lower() or
            keyword_lower in remark.lower() or
            keyword_lower in document_content.lower()):
            found_keywords += 1

    return found_keywords


def _count_exact_keyword_matches_in_chunk(keywords, title, remark, document_content):
    """Count how many different keywords have exact word matches in a chunk."""
    import re

    found_exact_keywords = 0
    for keyword in keywords:
        keyword_lower = keyword.lower().strip()
        if not keyword_lower:
            continue

        # Create regex pattern for exact word matching
        # \b ensures word boundaries (so "port" won't match "portal" or "import")
        pattern = r'\b' + re.escape(keyword_lower) + r'\b'

        # Check if exact keyword exists in any field
        if (re.search(pattern, title.lower()) or
            re.search(pattern, remark.lower()) or
            re.search(pattern, document_content.lower())):
            found_exact_keywords += 1

    return found_exact_keywords


def _calculate_keyword_match_score(keywords, title, remark, document_content):
    """
    Calculate a comprehensive keyword match score that prioritizes exact matches.
    Returns: (exact_matches, total_matches, weighted_score)
    """
    exact_matches = _count_exact_keyword_matches_in_chunk(keywords, title, remark, document_content)
    total_matches = _count_keyword_matches_in_chunk(keywords, title, remark, document_content)

    # Calculate weighted score: exact matches get much higher weight
    weighted_score = (exact_matches * 10) + (total_matches * 1)

    return exact_matches, total_matches, weighted_score


def _prioritize_chunks_by_keyword_matches(filtered_chunks, keywords):
    """
    Prioritize chunks based on exact and partial keyword matches:
    1. Calculate exact matches and total matches for each chunk
    2. Prioritize chunks with exact keyword matches first
    3. Within exact matches, sort by number of exact matches
    4. Apply smart filtering based on high-relevance chunks

    Returns: (high_priority_chunks, chunks_to_process)
    """
    import logging
    logger = logging.getLogger(__name__)

    if not keywords or not filtered_chunks:
        return [], filtered_chunks

    # Calculate match scores for each chunk
    exact_match_chunks = []
    partial_match_chunks = []

    for chunk in filtered_chunks:
        metadata = chunk["metadata"]
        title = metadata.get("Title", "")
        remark = metadata.get("Remark", "")
        document_content = chunk["document"]

        # Get comprehensive match information
        exact_matches, total_matches, weighted_score = _calculate_keyword_match_score(
            keywords, title, remark, document_content
        )

        # Store match information in the chunk
        chunk["exact_keyword_matches"] = exact_matches
        chunk["total_keyword_matches"] = total_matches
        chunk["keyword_match_score"] = weighted_score
        chunk["keyword_match_count"] = total_matches  # For backward compatibility

        # Categorize chunks
        if exact_matches > 0:
            exact_match_chunks.append(chunk)
        elif total_matches > 0:
            partial_match_chunks.append(chunk)

    # Sort exact match chunks by exact matches (descending), then by total matches
    exact_match_chunks.sort(key=lambda x: (x["exact_keyword_matches"], x["total_keyword_matches"]), reverse=True)

    # Sort partial match chunks by total matches
    partial_match_chunks.sort(key=lambda x: x["total_keyword_matches"], reverse=True)

    # Log the distribution
    logger.info(f"Keyword match distribution: "
                f"Exact matches: {len(exact_match_chunks)}, "
                f"Partial matches: {len(partial_match_chunks)}")

    if exact_match_chunks:
        logger.info(f"Top exact match chunks:")
        for i, chunk in enumerate(exact_match_chunks[:3]):
            title = chunk["metadata"].get("Title", "N/A")
            exact = chunk["exact_keyword_matches"]
            total = chunk["total_keyword_matches"]
            logger.info(f"  {i+1}. Exact: {exact}, Total: {total}, Title: {title[:50]}...")

    # Apply the enhanced smart filtering strategy
    high_relevance_chunks = exact_match_chunks  # Exact matches are always high relevance

    # Add high partial match chunks (2+ matches) to high relevance if we don't have too many exact matches
    if len(exact_match_chunks) <= 15:
        high_partial_chunks = [chunk for chunk in partial_match_chunks if chunk["total_keyword_matches"] >= 2]
        high_relevance_chunks.extend(high_partial_chunks[:max(0, 20 - len(exact_match_chunks))])

    # Determine what to rerank
    if len(high_relevance_chunks) <= 20:
        # Strategy: Prioritize high-relevance chunks, rerank the rest
        remaining_chunks = [chunk for chunk in partial_match_chunks
                          if chunk not in high_relevance_chunks]

        logger.info(f"Using exact match prioritization: {len(high_relevance_chunks)} high-relevance chunks "
                   f"(exact: {len(exact_match_chunks)}), processing {len(remaining_chunks)} remaining chunks")

        return high_relevance_chunks, remaining_chunks
    else:
        # Strategy: Too many high-relevance chunks, rerank all
        logger.info(f"Using standard filtering: {len(high_relevance_chunks)} high-relevance chunks > 20, "
                   f"processing all {len(filtered_chunks)} chunks")

        return [], filtered_chunks


def _intelligent_search_process(user_message, embedding, source_filter=None):
    """Execute the simplified intelligent search process."""
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"Starting intelligent search for: '{user_message}' with source_filter: {source_filter}")

        # Step 1: Extract keywords using Ollama
        logger.info("Step 1: Extracting keywords with Ollama")
        keywords = _extract_keywords_with_ollama(user_message)
        logger.info(f"Keywords extracted: {keywords}")

        if keywords:
            # Case 1: Keywords found - use keyword filtering first
            logger.info(f"Case 1: Keywords found ({len(keywords)}), using keyword-based search")

            # Get keyword-filtered chunks
            filtered_chunks = _filter_chunks_by_metadata(keywords, source_filter)
            logger.info(f"Keyword filtering found {len(filtered_chunks)} matching chunks")

            if filtered_chunks:
                # Hybrid approach: Get keyword-filtered chunks + embedding similarity chunks
                from ..core.config import INTELLIGENT_QUERY_CONFIG

                keyword_chunk_count = INTELLIGENT_QUERY_CONFIG["keyword_filtered_chunks"]
                embedding_chunk_count = INTELLIGENT_QUERY_CONFIG["embedding_similarity_chunks"]

                logger.info(f"Hybrid search: Getting top {keyword_chunk_count} keyword-filtered + top {embedding_chunk_count} embedding similarity chunks")

                # NEW: Apply keyword-based prioritization strategy
                logger.info("🔍 Applying keyword-based prioritization strategy")
                logger.info(f"📊 Input: {len(filtered_chunks)} filtered chunks, keywords: {keywords}")
                high_priority_chunks, chunks_to_rerank = _prioritize_chunks_by_keyword_matches(filtered_chunks, keywords)

                logger.info(f"📈 Prioritization result: {len(high_priority_chunks)} high-priority, {len(chunks_to_rerank)} to rerank")

                # Part 1: Handle high-priority chunks (2+ keyword matches)
                if high_priority_chunks:
                    logger.info(f"Found {len(high_priority_chunks)} high-priority chunks (2+ keyword matches)")

                    # Take top high-priority chunks up to keyword_chunk_count
                    high_priority_subset = high_priority_chunks[:keyword_chunk_count]

                    # Log top high-priority chunks for debugging
                    logger.info(f"Top {len(high_priority_subset)} high-priority chunks:")
                    for i, chunk in enumerate(high_priority_subset[:3]):  # Log top 3
                        match_count = chunk.get("keyword_match_count", 0)
                        score = chunk.get("keyword_score", 0)
                        title = chunk["metadata"].get("Title", "N/A")
                        logger.info(f"  {i+1}. Matches: {match_count}, Score: {score:.1f}, Title: {title[:50]}...")

                    high_priority_results = {
                        "ids": [[chunk["id"] for chunk in high_priority_subset]],
                        "documents": [[chunk["document"] for chunk in high_priority_subset]],
                        "metadatas": [[chunk["metadata"] for chunk in high_priority_subset]],
                        "distances": [[0.0] * len(high_priority_subset)]  # Default distances for high-priority
                    }
                else:
                    high_priority_results = {"ids": [[]], "documents": [[]], "metadatas": [[]], "distances": [[]]}

                # Part 2: Handle chunks that need reranking
                if chunks_to_rerank:
                    logger.info(f"🔄 Processing {len(chunks_to_rerank)} additional chunks (reranking removed)")
                    logger.info(f"📝 Query: '{user_message}'")
                    logger.info(f"🎯 Keywords used: {keywords}")

                    # Log sample chunks being processed
                    for i, chunk in enumerate(chunks_to_rerank[:3]):
                        title = chunk.get("metadata", {}).get("Title", "No title")
                        logger.info(f"   Chunk {i+1}: {title}")

                    # Use simple distance-based scoring (reranking removed)
                    reranked_chunks = chunks_to_rerank

                    # Convert chunks back to ChromaDB format
                    reranked_results = _chunks_to_chromadb_format(reranked_chunks)

                    logger.info(f"✅ Processing completed for {len(reranked_chunks)} chunks")

                    # Limit reranked results
                    remaining_slots = max(0, keyword_chunk_count - len(high_priority_results.get("ids", [[]])[0]))
                    if remaining_slots > 0 and reranked_results.get("ids") and reranked_results["ids"][0]:
                        reranked_subset = {
                            "ids": [reranked_results["ids"][0][:remaining_slots]],
                            "documents": [reranked_results["documents"][0][:remaining_slots]],
                            "metadatas": [reranked_results["metadatas"][0][:remaining_slots]],
                            "distances": [reranked_results["distances"][0][:remaining_slots]]
                        }
                    else:
                        reranked_subset = {"ids": [[]], "documents": [[]], "metadatas": [[]], "distances": [[]]}
                else:
                    logger.info("ℹ️  NO ADDITIONAL PROCESSING NEEDED: All chunks are high-priority (exact matches)")
                    reranked_subset = {"ids": [[]], "documents": [[]], "metadatas": [[]], "distances": [[]]}

                # Part 3: Combine high-priority and reranked results
                combined_keyword_results = _combine_results(high_priority_results, reranked_subset)
                logger.info(f"Combined keyword results: {len(combined_keyword_results.get('ids', [[]])[0])} chunks")

                # Part 4: Get embedding similarity chunks (reranking removed)
                logger.info(f"Getting embedding similarity chunks to find top {embedding_chunk_count}")
                unfiltered_results = _query_all_chunks(embedding, source_filter, n_results=embedding_chunk_count)
                logger.info(f"Embedding similarity results: {len(unfiltered_results.get('ids', [[]])[0])} chunks")

                # Part 5: Combine results (reranking removed)
                logger.info("🔄 Combining keyword and embedding results")
                final_results = _combine_results(combined_keyword_results, unfiltered_results)
                logger.info(f"Final combined results: {len(final_results.get('ids', [[]])[0])} chunks")

                return final_results
            else:
                # No keyword matches found, fall back to embedding similarity
                from ..core.config import INTELLIGENT_QUERY_CONFIG
                fallback_chunk_count = INTELLIGENT_QUERY_CONFIG["no_keywords_fallback_chunks"]

                logger.info(f"❌ NO KEYWORD MATCHES: No chunks matched extracted keywords")
                logger.info(f"🔄 Falling back to embedding similarity search ({fallback_chunk_count} chunks)")
                logger.info("⚠️  RERANKING SKIPPED: No keyword-filtered chunks to rerank")
                results = _query_all_chunks(embedding, source_filter, n_results=fallback_chunk_count)
                logger.info(f"Fallback embedding results: {len(results.get('ids', [[]])[0])} chunks")
                return results

        else:
            # Case 2: No keywords - get embedding similarity chunks
            from ..core.config import INTELLIGENT_QUERY_CONFIG
            fallback_chunk_count = INTELLIGENT_QUERY_CONFIG["no_keywords_fallback_chunks"]

            logger.info(f"❌ NO KEYWORDS EXTRACTED: Using pure embedding similarity search ({fallback_chunk_count} chunks)")
            logger.info("⚠️  RERANKING SKIPPED: No keywords available for prioritization")

            results = _query_all_chunks(embedding, source_filter, n_results=fallback_chunk_count)
            logger.info(f"Embedding-only results: {len(results.get('ids', [[]])[0])} chunks")

            return results

    except Exception as e:
        logger.error(f"Intelligent search process failed: {e}")
        raise


def _query_all_chunks(embedding, source_filter=None, n_results=10):
    """Query all chunks with embedding similarity."""
    query_params = {
        "query_embeddings": [embedding],
        "n_results": n_results,
        "include": ["documents", "metadatas", "distances"]
    }
    if source_filter:
        query_params["where"] = {"Source": source_filter}

    return collection.query(**query_params)


def _query_specific_chunks(embedding, chunk_ids, source_filter=None):
    """Query specific chunks by IDs and rank them by embedding similarity."""
    from ..core.database import collection

    if not chunk_ids:
        return {"ids": [[]], "documents": [[]], "distances": [[]], "metadatas": [[]]}

    # Get the specific chunks
    get_params = {
        "ids": chunk_ids,
        "include": ["documents", "metadatas", "embeddings"]
    }
    if source_filter:
        get_params["where"] = {"Source": source_filter}

    try:
        chunk_data = collection.get(**get_params)

        if not chunk_data.get("ids") or not chunk_data["ids"]:
            return {"ids": [[]], "documents": [[]], "distances": [[]], "metadatas": [[]]}

        # Calculate distances manually if embeddings are available
        if chunk_data.get("embeddings"):
            import numpy as np
            import logging
            logger = logging.getLogger(__name__)

            try:
                query_embedding = np.array(embedding, dtype=np.float32)
                distances = []

                logger.info(f"Query embedding shape: {query_embedding.shape}")
                logger.info(f"Number of chunk embeddings: {len(chunk_data['embeddings'])}")

                for i, chunk_embedding in enumerate(chunk_data["embeddings"]):
                    try:
                        chunk_emb = np.array(chunk_embedding, dtype=np.float32)
                        logger.info(f"Chunk {i} embedding shape: {chunk_emb.shape}")

                        # Calculate cosine distance with error handling
                        dot_product = np.dot(query_embedding, chunk_emb)
                        norm_query = np.linalg.norm(query_embedding)
                        norm_chunk = np.linalg.norm(chunk_emb)

                        # Avoid division by zero
                        if norm_query == 0 or norm_chunk == 0:
                            cosine_distance = 1.0  # Maximum distance for zero vectors
                        else:
                            cosine_similarity = dot_product / (norm_query * norm_chunk)
                            cosine_distance = 1 - cosine_similarity

                        distances.append(float(cosine_distance))

                    except Exception as chunk_error:
                        logger.error(f"Error processing chunk {i} embedding: {chunk_error}")
                        distances.append(1.0)  # Default high distance for failed chunks

                # Sort by distance (ascending - lower is better)
                sorted_indices = sorted(range(len(distances)), key=lambda i: distances[i])

                # Reorder results by distance
                sorted_ids = [chunk_data["ids"][i] for i in sorted_indices]
                sorted_documents = [chunk_data["documents"][i] for i in sorted_indices]
                sorted_metadatas = [chunk_data["metadatas"][i] for i in sorted_indices]
                sorted_distances = [distances[i] for i in sorted_indices]

                return {
                    "ids": [sorted_ids],
                    "documents": [sorted_documents],
                    "metadatas": [sorted_metadatas],
                    "distances": [sorted_distances]
                }

            except Exception as embedding_error:
                logger.error(f"Error in embedding similarity calculation: {embedding_error}")
                # Fall back to returning results without distance sorting
                return {
                    "ids": [chunk_data["ids"]],
                    "documents": [chunk_data["documents"]],
                    "metadatas": [chunk_data["metadatas"]],
                    "distances": [[0.0] * len(chunk_data["ids"])]
                }
        else:
            # No embeddings available, return in original order
            return {
                "ids": [chunk_data["ids"]],
                "documents": [chunk_data["documents"]],
                "metadatas": [chunk_data["metadatas"]],
                "distances": [[0.0] * len(chunk_data["ids"])]  # Default distances
            }

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error querying specific chunks: {e}")
        return {"ids": [[]], "documents": [[]], "distances": [[]], "metadatas": [[]]}


def _advanced_rerank_results(filtered_results, unfiltered_results, keywords=None):
    """Advanced reranking algorithm that combines multiple signals for better relevance."""
    import logging
    from ..core.config import INTELLIGENT_QUERY_CONFIG

    logger = logging.getLogger(__name__)

    # Check if advanced reranking is enabled
    if not INTELLIGENT_QUERY_CONFIG.get("enable_advanced_reranking", True):
        return _combine_results(filtered_results, unfiltered_results)

    logger.info("Applying advanced reranking algorithm")

    # Collect all chunks with their scores
    all_chunks = []

    # Process keyword-filtered chunks (higher base score)
    if filtered_results.get("ids"):
        filtered_ids = filtered_results["ids"][0]
        filtered_docs = filtered_results["documents"][0]
        filtered_metas = filtered_results["metadatas"][0]
        filtered_dists = filtered_results["distances"][0]

        for i, chunk_id in enumerate(filtered_ids):
            chunk_data = {
                "id": chunk_id,
                "document": filtered_docs[i] if i < len(filtered_docs) else "",
                "metadata": filtered_metas[i] if i < len(filtered_metas) else {},
                "distance": filtered_dists[i] if i < len(filtered_dists) else 0.0,
                "source": "keyword_filtered",
                "base_score": 1.0  # High base score for keyword matches
            }
            all_chunks.append(chunk_data)

    # Process embedding similarity chunks
    if unfiltered_results.get("ids"):
        unfiltered_ids = unfiltered_results["ids"][0]
        unfiltered_docs = unfiltered_results["documents"][0]
        unfiltered_metas = unfiltered_results["metadatas"][0]
        unfiltered_dists = unfiltered_results["distances"][0]

        # Get set of already included IDs to avoid duplicates
        existing_ids = {chunk["id"] for chunk in all_chunks}

        for i, chunk_id in enumerate(unfiltered_ids):
            if chunk_id not in existing_ids:
                chunk_data = {
                    "id": chunk_id,
                    "document": unfiltered_docs[i] if i < len(unfiltered_docs) else "",
                    "metadata": unfiltered_metas[i] if i < len(unfiltered_metas) else {},
                    "distance": unfiltered_dists[i] if i < len(unfiltered_dists) else 1.0,
                    "source": "embedding_similarity",
                    "base_score": 0.7  # Lower base score for embedding-only matches
                }
                all_chunks.append(chunk_data)

    # Apply advanced scoring
    for chunk in all_chunks:
        chunk["final_score"] = _calculate_advanced_score(chunk, keywords)

    # Sort by final score (higher is better)
    all_chunks.sort(key=lambda x: x["final_score"], reverse=True)

    logger.info(f"Reranked {len(all_chunks)} chunks using advanced algorithm")

    # Convert back to ChromaDB format
    return {
        "ids": [[chunk["id"] for chunk in all_chunks]],
        "documents": [[chunk["document"] for chunk in all_chunks]],
        "metadatas": [[chunk["metadata"] for chunk in all_chunks]],
        "distances": [[1.0 - chunk["final_score"] for chunk in all_chunks]]  # Convert score back to distance
    }


def _calculate_advanced_score(chunk, keywords=None):
    """Calculate advanced relevance score using multiple signals."""
    from ..core.config import INTELLIGENT_QUERY_CONFIG

    # Start with base score
    score = chunk["base_score"]

    # Apply keyword boost
    if chunk["source"] == "keyword_filtered":
        keyword_boost = INTELLIGENT_QUERY_CONFIG.get("keyword_boost_factor", 1.5)
        score *= keyword_boost

    # Apply metadata boosts
    metadata_boosts = INTELLIGENT_QUERY_CONFIG.get("metadata_boost_factors", {})
    metadata = chunk["metadata"]

    # Check if chunk comes from title or remark
    if metadata.get("Title"):
        title_boost = metadata_boosts.get("Title", 1.0)
        score *= title_boost

    if metadata.get("Remark"):
        remark_boost = metadata_boosts.get("Remark", 1.0)
        score *= remark_boost

    # Apply embedding similarity (lower distance = higher score)
    embedding_score = max(0.0, 1.0 - chunk["distance"])
    score *= (0.3 + 0.7 * embedding_score)  # Blend with embedding similarity

    # Additional keyword matching in content
    if keywords:
        content_lower = chunk["document"].lower()
        keyword_matches = sum(1 for keyword in keywords if keyword.lower() in content_lower)
        if keyword_matches > 0:
            score *= (1.0 + 0.1 * keyword_matches)  # 10% boost per keyword match

    return min(score, 2.0)  # Cap maximum score


def _combine_results(filtered_results, unfiltered_results):
    """Combine filtered and unfiltered results with duplicate removal logging."""
    import logging
    logger = logging.getLogger(__name__)

    # Get IDs from both result sets
    filtered_ids = set(filtered_results.get("ids", [[]])[0] if filtered_results.get("ids") else [])
    unfiltered_ids = unfiltered_results.get("ids", [[]])[0] if unfiltered_results.get("ids") else []

    # Start with filtered results (higher priority)
    combined_ids = list(filtered_results.get("ids", [[]])[0] if filtered_results.get("ids") else [])
    combined_documents = list(filtered_results.get("documents", [[]])[0] if filtered_results.get("documents") else [])
    combined_metadatas = list(filtered_results.get("metadatas", [[]])[0] if filtered_results.get("metadatas") else [])
    combined_distances = list(filtered_results.get("distances", [[]])[0] if filtered_results.get("distances") else [])

    initial_filtered_count = len(combined_ids)
    duplicates_removed = 0

    # Add unfiltered results that aren't already in filtered results
    if unfiltered_results.get("ids"):
        unfiltered_docs = unfiltered_results.get("documents", [[]])[0]
        unfiltered_metas = unfiltered_results.get("metadatas", [[]])[0]
        unfiltered_dists = unfiltered_results.get("distances", [[]])[0]

        for i, uid in enumerate(unfiltered_ids):
            if uid not in filtered_ids:
                combined_ids.append(uid)
                combined_documents.append(unfiltered_docs[i] if i < len(unfiltered_docs) else "")
                combined_metadatas.append(unfiltered_metas[i] if i < len(unfiltered_metas) else {})
                combined_distances.append(unfiltered_dists[i] if i < len(unfiltered_dists) else 1.0)
            else:
                duplicates_removed += 1

    final_count = len(combined_ids)

    logger.info(f"📊 Chunk combination: {initial_filtered_count} keyword + {len(unfiltered_ids)} embedding = {final_count} total")
    if duplicates_removed > 0:
        logger.info(f"🔄 Removed {duplicates_removed} duplicates (same chunks found by both methods)")

    return {
        "ids": [combined_ids],
        "documents": [combined_documents],
        "metadatas": [combined_metadatas],
        "distances": [combined_distances]
    }


# Reranking functions removed - using simple distance-based ranking


# All reranking functions removed

def _chunks_to_chromadb_format(chunks: list) -> dict:
    """
    Convert list of chunk dictionaries to ChromaDB query result format.

    Args:
        chunks: List of chunk dictionaries

    Returns:
        ChromaDB format dictionary with ids, documents, metadatas, distances
    """
    if not chunks:
        return {"ids": [[]], "documents": [[]], "metadatas": [[]], "distances": [[]]}

    ids = [chunk.get("id", "") for chunk in chunks]
    documents = [chunk.get("document", "") for chunk in chunks]
    metadatas = [chunk.get("metadata", {}) for chunk in chunks]
    distances = [chunk.get("distance", 0.5) for chunk in chunks]

    return {
        "ids": [ids],
        "documents": [documents],
        "metadatas": [metadatas],
        "distances": [distances]
    }
